var debug = 0; 
var start_campana_title = 125;
var start_campana_bar_top = 377;
var start_campana_bar_ind_top = 352;
var myDelay = 5;
var myTimeout = 500;

var myValue1 = 0;
var myValue2 = 0;
var myValueSum = 0;
var myValue1PX = 0;
var myValue2PX = 0;
var myValueSumPX = 0;
var soglia_ok = 0;
var myValue1C = 0;
var myValue2C = 0;
var myBonus = 0;

/* general effects*/
opacityEnter = {type: 'opacity', from:0, to:100, step: 1, delay:10}

function setMyVars(v1, v2, vc1, vc2, so_ok, bon)
{
	myAlert("setMyVars");
	myAlert("set: "+v1+" - "+v2);
	myValue1 = parseInt(v1);
	myValue2 = parseInt(v2);
	myValueSum = parseInt(myValue1 + myValue2);
	
	if(myValueSum == 0)
	{
			myValueSum = 1;
	}
	
	myAlert("myValue1: "+myValue1 );
	myAlert("myValue2: "+myValue2 );
	myAlert("myValueSum: "+myValueSum );
	
	if(myValueSum < 22000)
		myValueSumPX = Math.round(myValueSum/100);
	else
		myValueSumPX = 220;
	
	var test_var = Math.round((0 * 0)/ 1);
	myAlert("test_var: "+test_var );
	
	myValue1PX = Math.round((myValueSumPX * myValue1) / myValueSum);
	myValue2PX = Math.round((myValueSumPX * myValue2) / myValueSum);

	myAlert("myValueSumPX: "+myValueSumPX );
	myAlert("myValue1PX: "+myValue1PX );
	myAlert("myValue2PX: "+myValue2PX );
	
	myValue1C = vc1;
	myValue2C = vc2;
	//myAlert("myValueSumPX: "+myValueSumPX );
	//myAlert("myValue1_perc: "+myValue1_perc);
	soglia_ok = so_ok;	
	myBonus = bon;
}

function startEffects()
{
	myAlert("startEffects");
	//myAlert("Start Animation: "+myValueSum);
	var campanaTitle = getDiv('campana_title');
	
	//moveCampanaTitle = {type: 'top',from: start_campana_title, to: (start_campana_title-190), step: -1, delay: campana_delay} 
	
	campanaTitle.style.top = (start_campana_title + (220 - myValueSumPX))+"px";
	showDiv('campana_title');
	setTimeout(campanaMoved, myTimeout);
}

function campanaMoved(){
  myAlert("campanaMoved");   
	//myAlert("Start blue bar to: "+myValueSent);
	var campanaBlueBar = getDiv('campana_blue_bar');
	
	myAlert("campanaMoved 1");
	
	var moveCampanaBlueBarTop = {type: 'top',from: start_campana_bar_top, to: (start_campana_bar_top - myValue1PX), step: -2, delay: myDelay}
	var moveCampanaBlueBarHeight = {type: 'height',from: 0, to: myValue1PX, step: 2, delay: myDelay}

	myAlert("start_campana_bar_top - myValue1PX: "+start_campana_bar_top+" - "+myValue1PX+" ");

	myAlert("moveCampanaBlueBarTop: from: "+start_campana_bar_top+", to: "+(start_campana_bar_top - myValue1PX)+",");
	
	myAlert("moveCampanaBlueBarHeight: from: 0, to: "+myValue1PX+",");
	
	var campanaBlueBarInd = getDiv('campana_blue_bar_ind');
	var campanaBlueBarIndTop = {type: 'top',from: start_campana_bar_ind_top, to: (start_campana_bar_ind_top - Math.round(myValue1PX/2)), step: -1, delay: myDelay}
	
	myAlert("campanaBlueBarIndTop: from: "+start_campana_bar_ind_top+", to: "+(start_campana_bar_ind_top - Math.round(myValue1PX/2))+",");
	
	showDiv('campana_blue_bar');
	showDiv('campana_blue_bar_ind');
	
	myAlert("campanaMoved 4 b");
	
	$fx(campanaBlueBar).fxAdd(moveCampanaBlueBarTop).fxAdd(moveCampanaBlueBarHeight).fxAdd(opacityEnter).fxRun(startCampanaYellow);		
	//$fx(campanaBlueBarInd).fxAdd(campanaBlueBarIndTop).fxRun();			
	//$fx(campanaBlueBarInd).fxRun(startCampanaYellow);		
}

var startCampanaYellow = function(loopsDone){
  myAlert("startCampanaYellow");         
	//myAlert("Start blue bar to: "+myValueSent);
	var campanaYellowBar = getDiv('campana_yellow_bar');
	
	if(myValue2 != 0)
	{
		var moveCampanaYellowBarTop = {type: 'top',from: (start_campana_bar_top - myValue1PX), to: ((start_campana_bar_top - myValue1PX) - myValue2PX), step: -2, delay: myDelay}
		var moveCampanaYellowBarHeight = {type: 'height',from: 0, to: myValue2PX, step: 2, delay: myDelay}
		
		var campanaYellowBarInd = getDiv('campana_yellow_bar_ind');
		var campanaYellowBarIndTop = {type: 'top',from: (start_campana_bar_ind_top - myValue1PX), to: ((start_campana_bar_ind_top - myValue1PX) - Math.round(myValue2PX/2)), step: -1, delay: myDelay}		
	}
	else
	{
		var moveCampanaYellowBarTop = {type: 'top',from: start_campana_bar_top, to: start_campana_bar_top, step: -2, delay: myDelay}
		var moveCampanaYellowBarHeight = {type: 'height',from: 0, to: 0, step: 2, delay: myDelay}
		
		var campanaYellowBarInd = getDiv('campana_yellow_bar_ind');
		var campanaYellowBarIndTop = {type: 'top',from: start_campana_bar_ind_top, to: start_campana_bar_ind_top, step: -1, delay: myDelay}			
	}
			
	showDiv('campana_yellow_bar');
	showDiv('campana_yellow_bar_ind');
	$fx(campanaYellowBar).fxAdd(moveCampanaYellowBarTop).fxAdd(moveCampanaYellowBarHeight).fxAdd(opacityEnter).fxRun();	
	$fx(campanaYellowBarInd).fxAdd(campanaYellowBarIndTop).fxRun(startCampanaResult);
}

var startCampanaResult = function(loopsDone){
	
	myAlert("startCampanaResult"); 
  //myAlert("myValue1_perc: "+myValue1_perc);
  if(soglia_ok == '1')
  {
  	myAlert("startCampanaResult 1: ");
		var campana_result2 = getDiv('campana_result2');
		
		myAlert("startCampanaResult 1: 1");
		
		showDiv('campana_result2');
		
		myAlert("startCampanaResult 1: 2");
		

		setTimeout(startRaccolta, myTimeout);
		myAlert("startCampanaResult 1: 3");	
  }
  else
  {
  	myAlert("startCampanaResult 0: ");
		var campana_result1 = getDiv('campana_result1');
		
		showDiv('campana_result1');
		
		setTimeout(startRaccolta, myTimeout);
  }	      
}


function startRaccolta(){
  
  myAlert("startRaccolta"); 
        
	//myAlert("Start blue bar to: "+myValueSent);
	var raccoltaCont = getDiv('raccolta_container');
	
	showDiv('raccolta_container');
	
	setTimeout(startRaccolta2, myTimeout);

}
function startRaccolta2(){
        
  myAlert("startRaccolta2");       
        
	//myAlert("Start blue bar to: "+myValueSent);
	var raccoltaYellowBar = getDiv('raccolta_bar_yellow');
	var raccoltaBlueBar = getDiv('raccolta_bar_blue');
	
	/*
	step 1: 341
	step 2: 501
	step 3: 620
	*/

	if(myValue2C < 6190000)	
		myValue2_toX = Math.round(myValue2C/10000);
	else
		myValue2_toX = 619;

	if(myValue1C < 24760000)		
		myValue1_toX = Math.round(myValue1C/40000);
	else
		myValue1_toX = 619;
		
	myAlert(myValue1C+" -> myValue1_toX: "+myValue1_toX);		
	myAlert(myValue2C+" -> myValue2_toX: "+myValue2_toX);		
	var raccoltaYellowBarW = {type: 'width',from: 0, to: myValue2_toX, step: 2, delay: myDelay/2}
	var raccoltaBlueBarW = {type: 'width',from: 0, to: myValue1_toX, step: 2, delay: myDelay/2}
	

	$fx(raccoltaYellowBar).fxAdd(raccoltaYellowBarW).fxRun(startRaccolta2Y);
	$fx(raccoltaBlueBar).fxAdd(raccoltaBlueBarW).fxRun(startRaccolta2B);
}
var startRaccolta2Y = function(loopsDone){
  
  myAlert("startRaccolta2Y");   
        
	//myAlert("Start blue bar to: "+myValueSent);
	var raccolta_capital = getDiv('raccolta_capital');
	
	showDiv('raccolta_capital');
	
	setTimeout(startSviluppo, myTimeout);

}
var startRaccolta2B = function(loopsDone){
  
  myAlert("startRaccolta2B"); 
        
	//myAlert("Start blue bar to: "+myValueSent);
	var raccolta_investimento = getDiv('raccolta_investimento');
	
	showDiv('raccolta_investimento');
	
	setTimeout(startSviluppo, myTimeout);
}

var sviluppoCheck = 0; 
function startSviluppo(){
  
  myAlert("startSviluppo");
  
  if(sviluppoCheck == 0)
  	sviluppoCheck = 1;
  else
  {	      
		//myAlert("Start blue bar to: "+myValueSent);
		var sviluppo_container = getDiv('sviluppo_container');
		var sviluppo_1 = getDiv('sviluppo_1');
		
		showDiv('sviluppo_container');
		showDiv('sviluppo_1');
		
		if(soglia_ok == '1')
			showDiv('campana_title_to_svil');
		
		/*
		var capital_level = 0;
		var dim_level = 0;
		
		if(myValue2C < 3400000)
			capital_level = 1;
		else if(myValue2C >= 3400000 && myValue2C < 5000000)
			capital_level = 2;
		else
			capital_level = 3;

		if(myValue1C < 13600000)
			dim_level = 1;
		else if(myValue1C >= 13600000 && myValue1C < 20000000)
			dim_level = 2;
		else
			dim_level = 3;			
		
		if(capital_level < dim_level)
			getDiv('raccolta_title'+capital_level).className = 'raccolta_title_sel';
		else if(capital_level > dim_level)
			getDiv('raccolta_title'+dim_level).className = 'raccolta_title_sel';
		else
			getDiv('raccolta_title'+dim_level).className = 'raccolta_title_sel';
		*/
		
		
		if(myBonus == '0.5')
			getDiv('raccolta_title1').className = 'raccolta_title_sel';
		else if(myBonus == '0.7')
			getDiv('raccolta_title2').className = 'raccolta_title_sel';
		else if(myBonus == '0.9')
			getDiv('raccolta_title3').className = 'raccolta_title_sel';

		setTimeout(startSviluppo2, myTimeout);

	}		
}

function startSviluppo2(){
  
  myAlert("startSviluppo2");
        
	//myAlert("Start blue bar to: "+myValueSent);
	var sviluppo_2 = getDiv('sviluppo_2');
	
	showDiv('sviluppo_2');
	
	setTimeout(startSviluppo3, myTimeout);

}
function startSviluppo3(){
  
  myAlert("startSviluppo3");       
	//alert("Start blue bar to: "+myValueSent);
	var sviluppo_3 = getDiv('sviluppo_3');
	
	showDiv('sviluppo_3');
	setTimeout(startInformativa, myTimeout);
}

function startInformativa(){
  
  myAlert("status_informativa");       
	//alert("Start blue bar to: "+myValueSent);
	var sviluppo_3 = getDiv('status_informativa');
	
	showDiv('status_informativa');
	
}


/* general functions */
function myGetElementById(id_name)
{
	if (document.getElementById && !window.opera)
	{
	  var obj = document.getElementById(id_name);         
	}
	else if(document.all)
	{
	  var obj = document.all.id_name;
	}   	
	
	return obj;		
}

function showDiv(divName)
{
	myGetElementById(divName).style.display = 'inline';
}

function hideDiv(divName)
{
	myGetElementById(divName).style.display = 'none';
}

function getDiv(divName)
{
	return myGetElementById(divName);
}

function myAlert(myMex)
{
	if(debug)
		alert(myMex);	
}