<?
$A1_running = $A1running;
$A2_running = $A2running;
$A3_running = $A3running;

$K = 4-$Status->polizzeA1_MS;
$K2 = 10-$Status->polizzeA1_MS;

if($K < 0)
	$K = 0;
	
if($K2 < 0)
	$K2 = 0;



if(isset($_GET['left']))
	$hand_count = $_GET['left'];
else if($showCDS2 && $K <= 0)
	$hand_count = $K2;
else
	$hand_count = $K;	


	
if($A1_running)
	$active_act = 1;
else
	$active_act = 0;

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Colpi di scena 2011" />
<link href="/Agente/Iniziative/37-ColpiDiScena/css/site.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/Agente/Iniziative/37-ColpiDiScena/js/fx.js"></script>
<script type="text/javascript" src="/Agente/Iniziative/37-ColpiDiScena/js/status_1.js"></script>

<title>Colpi di scena 2011</title>
</head>
<body onload="startEffects(<?=$hand_count?>, <?=$active_act?>);">
	<div id="contents">
		<?
			include("menu.php");
		?>
		<div id="center_1">
			<div id="logo_1">
			</div>
			
			<?if($showCDS1){?>	
				<div id="colpo_1">
				</div>
				<div id="colpo_1_1_desc">
					È importante cogliere l'obiettivo del<br/>
					1º Atto: insieme a quelli del 2º e 3º<br/>
					Atto, offre un Extrabonus del 2%<br/>
					su tutti i premi netti perfezionati
				</div>				
			<?}?>
			
			<div id="table_1">
			</div>
			<div id="left_cont">
				<div class="lc_row" id="tl_1">
					<span class="lc_title1a">1°</span>
					<span class="lc_title1b">ATTO</span>
				</div>	
				<div class="lc_row"  id="tl_2">
					<span class="lc_content">1° gennaio - 31 marzo 2011</span>
				</div>	
				<div class="lc_spacer">&nbsp;
				</div>
				<div class="lc_row"  id="tl_3">
					<span class="lc_title2"><i>Obiettivo minimo</i></span><br/>
					<span class="lc_content">4 contratti della linea Mente Serena</span>
				</div>	
				<div class="lc_spacer">&nbsp;
				</div>
				<div class="lc_row"  id="tl_4">
					<span class="lc_title2"><i>Incentivazione</i></span><br/>
					<span class="lc_content">10% dei premi netti perfezionati</span>
				</div>					
			</div>
			
			
			
			<?if($A2_running){?>
				<?if($Status->obiettivoA1 && $Status->obiettivoB2_A1){?>
					<div id="center_desc" class="cd_5_l">
						Il 1° Atto si è concluso e tutti<br/>
						e due gli obiettivi sono stati<br/>
						raggiunti. Bella magia!<br/>
						Ora è importante conquistare<br/>
						l'obiettivo minimo del 2° Atto
					</div>	
				<?}else if($Status->obiettivoA1 && !$Status->obiettivoB2_A1){?>
					<div id="center_desc" class="cd_5_l">
						Il 1° Atto si è concluso<br/>
						e l'obiettivo minimo<br/>
						è stato raggiunto. Quello<br/>
						del 2° Colpo di Scena no.<br/>
						Andrà meglio al prossimo Atto
					</div>					
				<?}else{?>
					<div id="center_desc" class="cd_5_l">
						Il 1° Atto si è concluso<br/>
						e l'obiettivo non è stato<br/>
						raggiunto. Peccato!<br/>
						Ma c'è modo di rifarsi<br/>
						nel 2° Atto
					</div>					
				<?}?>
			<?}else if($showCDS2){?>
				<?if($Status->obiettivoA1 && $Status->obiettivoB2_A1){?>
					<div id="center_desc" class="cd_6_l">
						ET VOILÀ!<br/>
						Conquistato il Bonus del<br/>
						1° Atto e l'obiettivo di contratti<br/>
						per l'Extrabonus. Ora conviene<br/>
						incrementare la produzione<br/>
						computabile
					</div>	
				<?}else if($Status->obiettivoA1 && !$Status->obiettivoB2_A1){?>
					<div id="center_desc" class="cd_6_l">
						ET VOILÀ! il Bonus del 1° Atto<br/>
						è stato raggiunto.<br/>
						Ora manca<?=$K2==1 ? '' : 'no'?> <?=$K2?> contratt<?=$K2==1 ? 'o' : 'i'?><br/>
						(più l'obiettivo del 2° Atto)<br/>
						per l'Extrabonus aggiuntivo<br/>
						del 5%!
					</div>					
				<?}else{?>
					<div id="center_desc"  class="cd_6_l">
						Manca<?=$K==1 ? '' : 'no'?> <?=$K?> contratt<?=$K==1 ? 'o' : 'i'?> per<br/>
						accedere all'incentivazione<br/>
						con il Bonus del 10%<br/>
						e <?=$K2?> contratt<?=$K2==1 ? 'o' : 'i'?> (più l'obiettivo<br/>
						del 2° Atto) per l'Extrabonus<br/>
						aggiuntivo del 5%!
					</div>					
				<?}?>
			<?}else{?>
				<?if($Status->obiettivoA1){?>
					<div id="center_desc"  class="cd_5_l">
						ET VOILÀ!<br/>
						Il Bonus del 1° Atto è stato<br/>
						raggiunto. Meglio non fermarsi<br/>
						però… potrebbe esserci un<br/>
						nuovo Colpo di Scena!
					</div>					
				<?}else{?>
					<div id="center_desc"  class="cd_3_l">
						Manca<?=$K==1 ? '' : 'no'?> <?=$K?> contratt<?=$K==1 ? 'o' : 'i'?> per<br/>
						accedere all'incentivazione<br/>
						con il Bonus del 10%
					</div>					
				<?}?>								
			<?}?>	
				
				
				
				
			<?if($showCDS2){?>	

				<div id="colpo_cont_1_2">
				</div>		
				<div id="colpo_2">
				</div>				
				<div id="right_2">
					Con 10 o più contratti<br/>
					Mente Serena<br/>
					e l'obiettivo del 2º Atto...<br/>
					<b>+5% di Extrabonus</b> sulla<br/>
					produzione Mente Serena<br/>
					del 1º Atto
				</div>		
			<?}?>
			
			<div id="right_3">
				<i>Per una "vista d'insieme"<br/>
				cliccare Verifica Status Bonus</i>
			</div>	
			<div id="right_4_1">
				NB: per il calcolo della produzione computabile e<br/>
				dell’importo di incentivazione erogabile faranno fede<br/>
				i dati ufficiali di Compagnia.
			</div>	
			<div id="hat">
			</div>		
			<div id="dx">
				<div id="dx_image">
				</div>				
			</div>		
			<div id="sx">
				<div id="sx_image">
				</div>					
			</div>		

			<?/*=number_format($milions,$decimals,',','.') */?>

			<div id="rbox_1_1">Contratti Mente<br/>Serena Emessi
			</div>
			<div id="rbox_1_2">
			</div>
			<div id="rbox_1_3"><?=$Status->polizzeA1_MS ?>
			</div>
			
			<div id="rbox_2_1">Bonus
			</div>
			<div id="rbox_2_2">
			</div>
			<div id="rbox_2_3">10
			</div>
			
			<div id="rbox_3_1">Produzione<br/>Computabile
			</div>
			<div id="rbox_3_2">
			</div>
			<div id="rbox_3_3"><?=number_format($Status->premiA1_MS,0,',','.') ?>
			</div>
			
			<div id="rbox_4a_1">Attuale Importo Erogabile
			</div>
			<div id="rbox_4b_1">€ <?=number_format($Status->premiA1_MS,0,',','.') ?> x <?=$Status->polizzeA1_MS >=4 ? '10' : '0' ?>% = 
			</div>
			<div id="rbox_4_2">
			</div>
			<div id="rbox_4_3"><?=number_format($Status->importoErogabileA1,0,',','.') ?>
			</div>			
			
		</div>
	</div>
</body>
</html><? echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>