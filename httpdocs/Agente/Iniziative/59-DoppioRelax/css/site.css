/*CSS Document*/
BODY { margin: 0px; padding: 0px; background-color: #FFFFFF; /*
background-attachment:scroll;
background-image:url(/Agente/Iniziative/7-ContaPunti/images/sfondo.gif);
background-position:center top;
background-repeat:repeat-x;
*/
	behavior: url(/js/csshover-3.0/csshover3.htc); }
IMG { display: block; margin: 0px; padding: 0px; border: 0px solid #000000; }
.img_text { display: inline; margin: 0px; padding: 0px; border: 0px solid #000000; behavior: url(/js/iepngfix-1.0/iepngfix.htc); }
OBJECT, EMBED { display: block; margin: 0px; padding: 0px; border: 0px solid #000000; }
HTML, BODY { height: 100%; }
DIV { font-family: Arial; font-size: 12px; }
/*BASE STYLE*/
UL { margin-top: 0px; margin-bottom: 0px; }
LI { margin-top: 0px; margin-bottom: 0px; }
SELECT { font-family: Arial; font-size: 12px; }
.float_none { float: none; }
TABLE { border: 0px solid #000000; padding: 0px; margin: 0px; }
TR { border: 0px solid #000000; padding: 0px; margin: 0px; }
TD { border: 0px solid #000000; padding: 5px 10px; margin: 0px; }
.clear { clear: left; }
.float_left { float: left; }
.centered { display: block; margin: 0 auto; }
A:link { color: #FFFFFF; text-decoration: none; }
A:visited { color: #FFFFFF; text-decoration: none; }
A:active { color: #FFFFFF; text-decoration: none; }
A:hover { color: #FFFFFF; font-weight: bold; }
.intro_link:link { color: #81B39B; text-decoration: none; }
.intro_link:visited { color: #81B39B; text-decoration: none; }
.intro_link:active { color: #81B39B; text-decoration: none; }
.intro_link:hover { color: #FFFFFF; font-weight: bold; }
.spacer_5 { height: 5px; }
.spacer_10 { height: 10px; }
.spacer_20 { height: 20px; }
.spacer_30 { height: 30px; }
/*FONTS*/
.yellow { color: #FFC200; }
.red { color: #C20000; }
.white_bold { color: #FFF; font-weight: bold; }
.font_18 { font-size: 18px; }
.font_12 { font-size: 12px !important; line-height: 12px !important; }
.font_12 { font-size: 12px; }
.font_13 { font-size: 13px; }
.font_15 { font-size: 15px; }
.black_16_bold { font-size: 16px; font-weight: bold; color: #000; }
/*MAIN TEMPLATE*/
#contents { float: left; width: 869px; height: 492px; background-color: #E7E7E7; border-top: 8px solid #E7E7E7; border-bottom: 8px solid #E7E7E7; border-left: 8px solid #E7E7E7; border-right: 8px solid #E7E7E7; overflow: hidden; }
#menu { float: left; position: relative; width: 869px; height: 23px; background-image: url(../images/menu_bg.png); background-repeat: repeat-x; clear: left; overflow: hidden; }
#center { float: left; position: relative; width: 869px; height: 469px; background-image: url(../images/bkg_status.jpg); clear: left; overflow: hidden; }
#menu_1 { float: left; position: absolute; top: 0px; left: 0px; width: 53px; height: 23px; line-height: 23px; font-size: 11px; color: #FFFFFF; text-align: center; }
#menu_2 { float: left; position: absolute; top: 0px; left: 53px; width: 97px; height: 23px; line-height: 23px; font-size: 11px; color: #FFFFFF; text-align: center; }
#menu_desc { color: #FFFF00; float: left; font-size: 12px; font-weight: bold; height: 23px; left: 308px; line-height: 23px; position: absolute; text-align: left; top: 0; width: 296px; }
#menu_6 { float: left; position: absolute; top: 0px; left: 784px; width: 85px; height: 23px; line-height: 23px; font-size: 11px; color: #FFFFFF; text-align: center; }
#menu_1, #menu_2, #menu_6 { cursor: pointer; }
#menu_1:hover { background-image: url(../images/menu_1roll.png); font-weight: bold; }
#menu_2:hover { background-image: url(../images/menu_2roll.png); font-weight: bold; }
#menu_6:hover { background-image: url(../images/menu_3roll.png); font-weight: bold; }
.menu_sel1 { background-image: url(../images/menu_1roll.png); font-weight: bold; }
.menu_sel2 { background-image: url(../images/menu_2roll.png); font-weight: bold; }
#menu { float: left; position: relative; width: 869px; height: 23px; background-image: url(../img/menu_back.gif); background-repeat: repeat-x; clear: left; overflow: hidden; }
#menu a{ display: block; width: 105px; height: 23px; line-height: 23px; color: #000; float: left; text-decoration: none; text-align: center; }
#menu a:hover{ background-color: #F3A416; font-weight: bold; }
#menu .selected{ background-color: #F3A416; font-weight: bold; }
#menu .last{ float: right; }
/*STATUS*/

#ball_cont { float: left; position: absolute; width: 59px; height: 59px;  //background-image: url(../images/ball.png);
overflow: hidden; top: 75px; left: 280px; display: none; }
#catapulta_cont { float: left; position: absolute; width: 326px; height: 309px; overflow: hidden; top: 25px; left: 0px; display: none; }
#catapulta { float: left; position: absolute; overflow: hidden; top: 0px; left: 0px; }
#castello_cont { float: left; position: absolute; width: 179px; height: 212px; overflow: hidden; top: 124px; left: 690px; }
#castello { float: left; position: absolute; overflow: hidden; top: 0px; left: 0px; }
#gruppo_info, #gruppo_info_title { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 80px; left: 18px; line-height: 14px; overflow: hidden; position: absolute; top: 35px; width: 200px; display: none; }
#gruppo_info_title { left: 18px; top: 13px; }
#confalone_cont { float: left; position: absolute; width: 167px; height: 223px; overflow: hidden; top: 246px; left: 0px; display: none; }
#confalone { float: left; position: absolute; overflow: hidden; top: 0px; left: 0px; }
#status_info_1 { display: none; }
#status_info_2 { display: none; }
#status_img { float: left; position: absolute; width: 58px; height: 58px; background-image: url(../images/status_img.png); behavior: url(/js/iepngfix-1.0/iepngfix.htc); overflow: hidden; top: 102px; left: 449px; }
#status_1_t { color: #653832; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 286px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 82px; width: 150px; }
#status_1_v { color: #FFFFFF; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 326px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 105px; width: 150px; }
#status_2_t { color: #653832; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 286px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 135px; width: 150px; }
#status_2_v { color: #FFFFFF; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 353px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 143px; width: 150px; }
#status_title { color: #653832; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 450px; line-height: 14px; overflow: hidden; position: absolute; top: 179px; width: 150px; }
#status_3_4_simbols { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 70px; left: 539px; line-height: 30px; overflow: hidden; position: absolute; text-align: right; top: 205px; width: 50px; }
#status_3_t { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 449px; line-height: 14px; overflow: hidden; position: absolute; top: 198px; width: 150px; }
#status_3_v { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 479px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 212px; width: 150px; }
#status_4_t { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 449px; line-height: 14px; overflow: hidden; position: absolute; top: 228px; width: 150px; }
#status_4_v { color: #000000; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 479px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 242px; width: 150px; }
#status_tot_t { color: #BA1B21; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 440px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 268px; width: 150px; }
#status_tot_v { color: #BA1B21; float: left; font-size: 12px; font-weight: bold; height: 40px; left: 479px; line-height: 14px; overflow: hidden; position: absolute; text-align: right; top: 268px; width: 150px; }
#status_final { color: #653832; float: left; font-size: 16px; font-weight: bold; height: 90px; left: 717px; line-height: 18px; overflow: hidden; position: absolute; text-align: center; top: 167px; width: 150px; display: none; }
#status_line_1 { background-color: #653832; float: left; height: 2px; left: 328px; overflow: hidden; position: absolute; top: 171px; width: 300px; }
#status_line_2 { background-color: #653832; float: left; height: 2px; left: 449px; overflow: hidden; position: absolute; top: 262px; width: 181px; }
#month_cont_4 { float: left; height: 33px; left: 318px; overflow: hidden; position: absolute; top: 312px; width: 43px; display: none; }
#month_cont_5 { float: left; height: 33px; left: 407px; overflow: hidden; position: absolute; top: 326px; width: 43px; display: none; }
#month_cont_6 { float: left; height: 33px; left: 498px; overflow: hidden; position: absolute; top: 340px; width: 43px; display: none; }
#month_cont_7 { float: left; height: 33px; left: 582px; overflow: hidden; position: absolute; top: 353px; width: 43px; display: none; }
#month_cont_8 { float: left; height: 33px; left: 663px; overflow: hidden; position: absolute; top: 367px; width: 43px; display: none; }
#month_cont_9 { float: left; height: 33px; left: 748px; overflow: hidden; position: absolute; top: 383px; width: 43px; display: none; }
#month_cont_10 { float: left; height: 33px; left: 822px; overflow: hidden; position: absolute; top: 397px; width: 43px; display: none; }
#month_4, #month_5, #month_6, #month_7, #month_8, #month_9, #month_10 { float: left; position: absolute; overflow: hidden; top: 0px; left: 0px; }
#nb { color: #FFFFFF; float: left; font-size: 8px; font-weight: bold; height: 20px; left: 180px; line-height: 10px; overflow: hidden; position: absolute; text-align: left; top: 454px; width: 500px; display: none; }
/*DETTAGLIO POLIZZE*/
#center_frame_container { float: left; width: 884px; height: 507px; overflow: hidden; position: relative; background-color: #E6E7E7; border-left: 1px solid #E6E7E7; border-top: 1px solid #E6E7E7; }
#content_container { float: left; width: 868px; height: 491px; top: 8px; left: 8px; overflow: hidden; position: relative; background-image: url(../images/bg_dett.png); }
#dettaglio_polizze_header { float: left; width: 868px; height: 62px; overflow: hidden; position: relative; }
#dp_page_back { float: left; width: 16px; height: 16px; overflow: hidden; position: absolute; top: 4px; left: 73px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	cursor: pointer; }
#dp_page_forw { float: left; width: 16px; height: 16px; overflow: hidden; position: absolute; top: 4px; left: 157px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	cursor: pointer; }
#dp_page_number { float: left; width: 68px; height: 16px; overflow: hidden; position: absolute; top: 4px; left: 89px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);*/
	line-height: 16px; font-size: 10px; color: #000000; text-align: center; }
#orderby_container { float: left; width: 315px; height: 275px; position: absolute; top: 20px; left: 400px; z-index: 9999;/*background-image:url('images/orderby_background.png');
behavior: url('/js/iepngfix-1.0/iepngfix.htc');
background-repeat: no-repeat;
background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
}
#orderby_background { float: left; width: 315px; height: 268px; z-index: 9999; background-image: url(../images/orderby_background.png); behavior: url(/js/iepngfix-1.0/iepngfix.htc); background-repeat: no-repeat;/*background-repeat: no-repeat;
background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/ }
#orderby_form { float: left; width: 315px; height: 268px; z-index: 9999;/*background-image:url('/Agente/images/orderby_background.png');
behavior: url('/js/iepngfix-1.0/iepngfix.htc');
background-repeat: no-repeat;
background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
}
#orderby_select_1 { float: left; width: 160px; height: 18px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 36px; left: 25px; z-index: 9999; }
#orderby_select_2 { float: left; width: 160px; height: 18px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 85px; left: 25px; z-index: 9999; }
#orderby_select_3 { float: left; width: 160px; height: 18px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 130px; left: 25px; z-index: 9999; }
#orderby_select_4 { float: left; width: 160px; height: 18px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 178px; left: 25px; z-index: 9999; }
#orderby_no { float: left; width: 44px; height: 16px; /*background-color: red;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 216px; left: 23px; z-index: 9999; cursor: pointer; line-height: 17px; padding: 0 0 0 20px; }
#orderby_yes { float: left; width: 37px; height: 16px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);*/
	position: absolute; top: 216px; left: 237px; z-index: 9999; cursor: pointer; line-height: 17px; padding: 0 0 0 6px; }
#orderby_no:hover { font-weight: bold; }
#orderby_yes:hover { font-weight: bold; }
#orderby_check_1_c { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_checked.png);*/
	position: absolute; top: 26px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_1_d { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_unchecked.png);*/
	position: absolute; top: 44px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_2_c { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_checked.png);*/
	position: absolute; top: 73px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_2_d { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_unchecked.png);*/
	position: absolute; top: 91px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_3_c { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_checked.png);*/
	position: absolute; top: 120px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_3_d { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_unchecked.png);*/
	position: absolute; top: 138px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_4_c { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_checked.png);*/
	position: absolute; top: 168px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_check_4_d { float: left; width: 12px; height: 12px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);
background-image:url(images/checkbox_unchecked.png);*/
	position: absolute; top: 186px; left: 197px; z-index: 9999; cursor: pointer; }
#orderby_button { float: left; width: 93px; height: 16px; overflow: hidden; position: absolute; top: 4px; left: 582px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);*/
	line-height: 16px; font-size: 11px; color: #818181; cursor: pointer; padding: 0 0 0 5px; }
#scarica_button { float: left; width: 141px; height: 16px; overflow: hidden; position: absolute; top: 4px; left: 695px; /*background-color: green;
opacity:0.5;
filter: alpha(opacity = 50);*/
	line-height: 16px; font-size: 11px; color: #818181; cursor: pointer; padding: 0 0 0 5px; }
#orderby_button:hover, #scarica_button:hover { color: #000000; font-weight: normal; }
/*FULLPAGE GRID*/
#fullpage_grid { position: absolute; top: 88px; left: 10px; width: 850px; /*829*/
	height: 360px; overflow: hidden; clear: both; }
/*
#grid_line_content {
border-bottom:2px solid #339930;
clear:both;
color:#FFFFFF;
font-size:10px;
height:17px;
line-height:17px;
text-align:center;
width:824px;
}
*/
.fullpage_grid_row { width: 829px; height: 17px; color: #000; font-weight: bold; clear: both; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #CCC; background-color: #FFF; }
.fullpage_grid_row DIV { line-height: 17px; font-size: 10px; }
.dp_grid_field_1 { float: left; width: 65px; text-align: center; }
.dp_grid_field_2 { float: left; width: 125px; text-align: center; }
.dp_grid_field_3 { float: left; width: 110px; text-align: left; padding: 0 0 0 25px; }
.dp_grid_field_4 { float: left; width: 224px; text-align: left; padding: 0 0 0 7px; }
.dp_grid_field_5 { float: left; width: 147px; text-align: center; }
.dp_grid_field_5b { float: left; padding-left: 0; text-align: right; width: 57px; }
.dp_grid_field_6 { float: left; padding: 0 15px 0 0; text-align: right; width: 110px; }
