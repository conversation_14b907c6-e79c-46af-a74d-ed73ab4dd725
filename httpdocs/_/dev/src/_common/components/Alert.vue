<script setup>
const props = defineProps({
    title: String,
    icon: Object,
    theme: {
        validator(value) {
            if (!value) {
                return 'default';
            }
            return ['error'].includes(value)
        }
    },
    text: [String, Object]
})
</script>

<template>
    <div class="alert" :class="theme">
        <div v-if="icon">
            <component :is="icon" class="h-10 text-white"></component>
        </div>
        <div>
            <h5 class="text-xl font-bold">{{ title }}</h5>
            <div v-if="typeof text === 'string'">
                {{text}}
            </div>
            <div v-if="typeof text === 'object'">
                <div v-for="(value, key) in text" :key="key">
                    <span style="text-transform: capitalize">{{ key }}</span>: {{ value }}.
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>