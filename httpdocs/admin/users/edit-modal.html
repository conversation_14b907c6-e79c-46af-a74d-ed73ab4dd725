<header>
	<i class="icon-pencil"></i> Anagrafica utente: {{User.cognome}} {{User.nome}}
	<div ng-click="$close()" class="x-close" aria-hidden="true">&times;</div>
</header>
<section ng-form="userForm" xng-form="xngFormUser" ng-model-options="{ updateOn: 'default blur', debounce: { default: 300, blur: 0 } }" class="x-panel">
	<div class="x-form-row">
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.type.$invalid && (userForm.type.$touched || userForm.$submitted)}">
			Tipo<br/>
			<select name="type" ng-model="User.type" ng-options="k as v for (k, v) in types">
			</select>
			<span ng-if="userForm.type.$touched" ng-messages="userForm.type.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.area.$invalid && (userForm.area.$touched || userForm.$submitted)}">
			Area<br/>
			<select name="area" ng-model="User.area" ng-options="a.id as a.nome for a in aree" class="x-large"></select>
			<span ng-if="userForm.area.$touched" ng-messages="userForm.area.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.district.$invalid && (userForm.district.$touched || userForm.$submitted)}">
			District<br/>
			<select name="district" ng-model="User.district" ng-options="d.id as d.nome for d in districts" class="x-large"></select>
			<span ng-if="userForm.district.$touched" ng-messages="userForm.district.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.agenzia_id.$invalid && (userForm.agenzia_id.$touched || userForm.$submitted)}">
			Agenzia<br/>
			<input name="agenzia_id" ng-model="User.agenzia_id" type="text" class="x-medium" maxlength="4">
			<span ng-if="userForm.agenzia_id.$touched" ng-messages="userForm.agenzia_id.$error" class="x-help-block">
					<span ng-message="regex">Formato non valagenzia_ido: 4 caratteri obbligatori</span>
				</span>
		</label>
	</div>
	<div class="x-form-row">
		<label class="x-col-1-sm x-col-12-xs" ng-class="{'x-error': userForm.isAzienda.$invalid && (userForm.isAzienda.$touched || userForm.$submitted)}">
			Azienda<br/>
			<input type="checkbox" name="isAzienda" ng-model="User.isAzienda" ng-true-value="true" ng-false-value="false"/>
		</label>
		<label ng-if="!User.isAzienda" class="x-col-2-lg x-col-5-sm x-col-12-xs" ng-class="{'x-error': userForm.nome.$invalid && (userForm.nome.$touched || userForm.$submitted)}">
			Nome<br/>
			<input name="nome" ng-model="User.nome" type="text" maxlength="50">
			<span ng-if="userForm.nome.$touched" ng-messages="userForm.nome.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-12-xs" ng-class="{'x-col-6-lg x-col-6-sm': !User.isAzienda, 'x-col-8-lg x-col-11-sm': User.isAzienda, 'x-error': userForm.cognome.$invalid && (userForm.cognome.$touched || userForm.$submitted)}">
			<span ng-if="!User.isAzienda">Cognome</span><span ng-if="User.isAzienda">Ragione sociale</span><br/>
			<input name="cognome" ng-model="User.cognome" type="text" maxlength="50">
			<span ng-if="userForm.cognome.$touched" ng-messages="userForm.cognome.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.cellulare.$invalid && (userForm.cellulare.$touched || userForm.$submitted)}">
			Cellulare<br/>
			<input name="cellulare" ng-model="User.cellulare" type="text" class="x-medium" maxlength="15">
			<span ng-if="userForm.cellulare.$touched" ng-messages="userForm.cellulare.$error" class="x-help-block">
					<span ng-message="regex">Formato non valcellulareo: 4 caratteri obbligatori</span>
					<span ng-message="unique">Codice già assegnato ad altro utente</span>
				</span>
		</label>
	</div>
	<div class="x-form-row">
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.piva.$invalid && (userForm.piva.$touched || userForm.$submitted)}">
			P.IVA<br/>
			<input name="piva" ng-model="User.piva" type="text" class="x-large" maxlength="11">
			<span ng-if="userForm.piva.$touched" ng-messages="userForm.piva.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.rui.$invalid && (userForm.rui.$touched || userForm.$submitted)}">
			RUI<br/>
			<input name="rui" ng-model="User.rui" type="text" class="x-large" maxlength="50">
			<span ng-if="userForm.rui.$touched" ng-messages="userForm.rui.$error" class="x-help-block">
					<span ng-message="REQUIRED">Campo obbligatorio</span>
					<span ng-message="REGEX">Attenzione: il codice deve essere composto da 9 caratteri e deve iniziare con lettera "E" o "B"</span>
					<span ng-message="UNIQUE">Attenzione: codice già utilizzato da altro utente</span>
					<span ng-message="BROKER">Attenzione: non è possibile inserire un codice con lettera "B" se prima non si disattiva l'accesso all'elearning</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.codEsazione.$invalid && (userForm.codEsazione.$touched || userForm.$submitted)}">
			Cod. Esazione<br/>
			<input type="text" name="codEsazione" ng-model="User.codEsazione" ng-disabled="User.type != 'INTERMEDIARIO'" class="x-medium" maxlength="15">
			<span ng-if="userForm.codEsazione.$touched" ng-messages="userForm.codEsazione.$error" class="x-help-block">
					<span ng-message="REQUIRED">Campo obbligatorio</span>
					<span ng-message="REGEX">Attenzione: il codice deve essere composto da 6 cifre</span>
					<span ng-message="UNIQUE">Attenzione: codice già utilizzato da altro utente</span>
				</span>
		</label>
		<label class="x-col-3-lg x-col-6-sm x-col-12-xs" ng-class="{'x-error': userForm.ruolo.$invalid && (userForm.ruolo.$touched || userForm.$submitted)}">
			Ruolo<br/>
			<select name="ruolo" ng-model="User.ruolo" ng-disabled="User.type != 'INTERMEDIARIO'" ng-options="k as v for (k, v) in roles">
				<option value=""></option>
			</select>
			<span ng-if="userForm.ruolo.$touched" ng-messages="userForm.ruolo.$error" class="x-help-block">
					<span ng-message="required">obbligatorio</span>
				</span>
		</label>
	</div>
	<section class="x-form-actions">
		<span ng-if="msg.loading" class="x-alert x-blue">Stiamo aggiornando il sistema, si prega di attendere. Non chiudere la pagina.</span>
		<span ng-if="msg.error" class="x-alert x-red">Ops, qualcosa è andato storto. L'operazione non è riuscita. Si prega di riprovare.</span>
		<a ng-disabled="loading" xng-form-submit="" class="x-btn x-blue"><i class="icon-disk"></i> Salva</a>
	</section>
</section>
