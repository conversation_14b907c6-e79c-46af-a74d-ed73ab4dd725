(function(ng, module) {

    module.config(['$stateProvider', 'xng.$dataProvider', function($stateProvider, $dataProvider) {
        $stateProvider.state('apps-gdpr',{
            url: '/apps/gdpr/',
            templateUrl: 'apps/gdpr/index.html',
            controller: "app.apps.gdpr.IndexController",
            navbar: 'apps'
        });

        $dataProvider
            .repository('apps.formaz.GdprCourses', { pkey: 'user_id',	url: '/api/rest-api/apps.formaz.legacy.GdprCourses' });

    }]);

    module.controller("app.apps.gdpr.IndexController", ['$scope', 'xng.$data', '$rootScope', function($scope, $data, $rootScope){
        var repository = $data.repository('apps.formaz.GdprCourses');

        $scope.data = null;
        $scope.showDeleteButton = false;

        repository.fetchAll().then(function(response){
            if (response && response.length) {
                $scope.data = response[0];
                $scope.data.date = new Date(response[0].date);
                $scope.showDeleteButton = true;
            }
        });

        $scope.save = function(data) {
            if (data.user_id) {
                return repository.update(data).then(updateModel);
            }

            $scope.showDeleteButton = true;

            data.user_id = $rootScope.$authData.UID;

            repository.add(data).then(updateModel);

            function updateModel(data) {
                data.date = new Date(data.date);

                $scope.data = data;

                alert("Dati salvati");
            }
        };

        $scope.delete = function() {

            var userConfirmation = confirm('Sei sicuro di voler eliminare il corso?');

            if (userConfirmation) {
                repository.remove($scope.data).then(function(success) {

                    if (success) {
                        $scope.showDeleteButton = false;
                        $scope.data = null;
                        alert("Corso eliminato.");
                        return;
                    }

                    alert("Impossibile eliminare il corso. Errore imprevisto.");

                });
            }

        }
    }]);

})(angular, angular.module('app.apps.gdpr', []));
