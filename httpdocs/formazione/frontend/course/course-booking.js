(function(angular, module){

    module.config(['$stateProvider', function($stateProvider) {

        $stateProvider.state('root.frontend.course.booking', {
            url: '/booking',
            views: {
                "tab": {
                    //templateUrl: 'frontend/course/booking.html',
                    templateUrl: 'frontend/course/temp.html',
                    controller: "CourseBookingController"
                }
            },
            resolve: {
                classrooms: function ($http, $handler, $stateParams) {
                    return $http({
                        method: 'GET',
                        url: '/api/apps/formazione/booking/course/' + $stateParams.courseId + '/available'
                    }).then(function (response) {

                        if (! response.data.success) {
                            return $handler.handle(response.data);
                        }

                        return response.data.data.classrooms;

                    });
                },
                employees: function ($http, $handler, $stateParams) {
                    return $http({
                        method: 'GET',
                        url: '/api/apps/formazione/booking/course/' + $stateParams.courseId + '/recipients'
                    }).then(function (response) {

                        if (! response.data.success) {
                            return $handler.handle(response.data);
                        }

                        return response.data.data;

                    });
                },
                userStatus: function($http, $handler, $stateParams) {
                    return $http({
                        method: 'POST',
                        url: '/api/apps/formazione/booking/status/' + $stateParams.courseId
                    }).then(function (response) {

                        if (! response.data.success) {
                            return $handler.handle(response.data);
                        }

                        return response.data.data;

                    });
                }
            },

        });

    }]);

    module.controller('CourseBookingController',  function($scope, $rootScope, $http, $handler, classrooms, swangular, userStatus) {
        $scope.user = $rootScope.$authData;
        $scope.user.id = $scope.user.UID;

        $scope.classrooms = classrooms;
        $scope.userStatus = userStatus;
        $scope.loading = {
            booking: false,
        };

        // Load locations.
        /*$http.get('/api/apps/formazione/booking/course/1/available').then(function(response){
            $scope.classrooms = response.data.data.classrooms;
        });*/

        $scope.setClassroom = function(classroom, index) {

            if ($scope.selectedId === classroom.id) {
                return $scope.selectedId = null;
            }

            $scope.classrooms[index].loading = true;

            $http.get('/api/apps/formazione/booking/classroom/' + classroom.id).then(function(response){
                $scope.selectedId = classroom.id;
                $scope.classrooms[index].loading = false;

                $scope.classrooms = updateClass($scope.classrooms, response.data.classroom);

                return response.data.classroom.id;
            }).then(loadEmployees).then(function(recipients){
                $scope.employees = recipients;
            })
        };

        $scope.book = function(user, classroom) {
            var data = {
                classroomId: classroom.id,
                userId: user.id,
                action: (classroom.vacantSeats > 0) ? 'booked' : 'queued'
            };

            var alertMessage = "Sicuro di voler procedere con questa prenotazione?";

            $http.get('/api/apps/formazione/booking/classroom/' + classroom.id).then(function(response) {
                $scope.classrooms = updateClass($scope.classrooms, response.data.classroom);

                if (classroom.vacantSeats < 1) {
                    alertMessage = 'I posti nella classe selezionata sono esauriti, proseguendo con la richiesta di prenotazione verrai messo in lista d\'attesa (sarai il numero ' + (classroom.queuedUsers + 1) + '). Vuoi comunque proseguire con la richiesta di prenotazione?';
                }

                swangular.confirm(alertMessage,{
                    title: 'Attenzione',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Conferma',
                    cancelButtonText: 'Annulla'
                }).then(function(result) {
                    if (result.value) {
                        proceedToBooking(data);
                    }
                });
            });

            function proceedToBooking(data)
            {
                $scope.loading.booking = true;

                $http.post('/api/apps/formazione/booking/book', data).then(function(response){
                    $scope.loading.booking = false;
                    if (! response.data.success) {
                        if (response.data.errorCode === 118) {
                            classroom.vacantSeats = 0;
                            return $scope.book(user, classroom);
                        }
                        return $handler.handle(response.data);
                    }

                    // Check user new status, issue warning if queued
                    var updatedStatus = getUpdatedStatus(response.data.bookingStatus, user.id);

                    switch (updatedStatus.state) {
                        case 'booked':
                            swangular.info("La tua richiesta di prenotazione è stata presa in carico. Riceverai una email di conferma iscrizione a seguito di validazione da parte della Compagnia.", {confirmButtonText: 'Ho capito', showCancelButton: false, title: "Prenotazione presa in carico"});
                            break;
                        case 'queued':
                            swangular.alert("La tua prenotazione in lista d'attesa è stata registrata. riceverai una email di notifica qualora dovesse liberarsi un posto.", {confirmButtonText: 'Ho capito', showCancelButton: false, title: "Prenotazione in lista d'attesa"});
                            break;
                    }

                    $scope.employees = response.data.bookingStatus;
                    //$scope.classroom = response.data.classroom;
                    $scope.classrooms = updateClass($scope.classrooms, response.data.classroom);
                    console.log(response.data.classroom);
                });
            }
        };

        function updateClass(classrooms, classroom) {
            angular.forEach(classrooms, function (current, index) {
                if (classroom.id == current.id) {
                    classrooms[index] = classroom;
                }
            });

            return classrooms;
        }

        function getUpdatedStatus(employees, userId) {
            return employees.find(x => x.id === userId);
        }

        function loadEmployees(classroomId) {
            return $http({
                method: 'GET',
                url: '/api/apps/formazione/booking/course/' + classroomId + '/recipients'
            }).then(function (response) {

                if (! response.data.success) {
                    return $handler.handle(response.data);
                }

                return response.data.data;
            });
        }

        $scope.getClassroomStatus = function (classroom) {
            var currentDate = new Date();
            var startDate = new Date(classroom.signupStartDate.split("/").reverse().join("-"));
            var endDate = new Date(classroom.signupEndDate.split("/").reverse().join("-"));

            if (currentDate >= startDate && currentDate <= endDate) {
                return 'active';
            }
            else if (currentDate > endDate) {
                return 'expired';
            }
            else if (currentDate < startDate) {
                return 'not started';
            }
        }

    });

})(angular, angular.module('course.booking', []));
