{"name": "toastr", "filename": "build/toastr.min.js", "main": "toastr.js", "style": "build/toastr.min.css", "version": "2.1.4", "description": "ToastrJS is a JavaScript library for Gnome / Growl type non-blocking notifications. jQuery is required. The goal is to create a simple core library that can be customized and extended.", "homepage": "http://www.toastrjs.com", "keywords": ["Toastr", "ToastrJS", "toastr.js"], "maintainers": [{"name": "<PERSON>", "web": "http://www.johnpapa.net", "twitter": "@john_papa"}, {"name": "<PERSON>", "web": "https://twitter.com/ferrell_tim", "twitter": "@ferrell_tim"}], "repository": {"type": "git", "url": "git://github.com/CodeSeven/toastr.git"}, "bugs": "http://stackoverflow.com/questions/tagged/toastr", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "dependencies": {"jquery": ">=1.12.0"}, "devDependencies": {"gulp": "^3.8.10", "gulp-bytediff": "^0.2.0", "gulp-jscs": "^1.3.0", "gulp-jshint": "^1.9.0", "gulp-less": "^3.0.3", "gulp-load-plugins": "^0.7.1", "gulp-load-utils": "0.0.4", "gulp-minify-css": "^0.3.11", "gulp-rename": "^1.2.0", "gulp-sourcemaps": "^1.2.8", "gulp-task-listing": "^0.3.0", "gulp-uglify": "^1.0.1", "gulp-util": "^3.0.1", "jquery": "^2.1.1", "jshint-stylish": "^1.0.0", "karma": "^0.12.25", "karma-coverage": "^0.2.6", "karma-phantomjs-launcher": "^0.1.4", "karma-qunit": "^0.1.3", "merge-stream": "^0.1.6", "phantomjs": "^1.9.7-15", "plato": "^1.2.2", "qunitjs": "~1.14.0"}, "scripts": {"test": "gulp test"}}