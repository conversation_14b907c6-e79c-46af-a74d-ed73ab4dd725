<div class="container">

    <div class="row">
        <div class="col">

            <div class="kt-portlet kt-portlet--mobile">

                <div class="kt-portlet__head kt-portlet__head--lg">
                    <div class="kt-portlet__head-label">
                        <span class="kt-portlet__head-icon">
                            <i class="octo-text-primary fas fa-chart-pie"></i>
                        </span>
                        <h3 class="kt-portlet__head-title font-weight-bold text-uppercase">
                            Sommario | <select class="form-control" id="selectedYear" ng-model="selectedYear"
                                             ng-options="year for year in years"
                                             ng-change="onSelectedYearChange()"
                                             style="font-size: 18px; font-weight: 700; padding: 0; border: 0; width: auto; display: inline"></select>
                        </h3>
                    </div>
                </div>

                <div class="kt-portlet__body">

                    <div class="row" ng-if="selectedArea">
                        <div class="col-sm-6 offset-sm-3">
                            <h2 class="text-center mb-2" style="color: #00624a; font-weight: 500">Dati area: <strong>{{selectedArea.title}}</strong></h2>
                            <button type="button" class="btn btn-outline-hover-dark btn-lg btn-block mb-5" ng-click="removeAreaFilter()">Rimuovi filtro su area</button>
                        </div>
                    </div>

                    <div class="row mb-5" ng-if=" ($authData.UTYPE === 'KA' || $authData.UTYPE === 'AMMINISTRATORE') && !selectedArea ">
                        <div class="offset-sm-2 col-sm-8">
                            <h4 class="text-center mb-3" style="color: #00624a; font-weight: 500">Numero iniziative per Area</h4>
                            <div class="chart-container" style="position: relative; height: 100%; width: 100%">
                                <canvas id="initiative-counter" class="chart chart-polar-area"
                                        chart-data="dataInitiative" chart-labels="labelsInitiative" chart-options="optionsInitiative">
                                </canvas>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6" style="border-right: 1px solid #ebedf2">
                            <h4 class="text-center mb-3" style="color: #00624a; font-weight: 500">Rapporto obiettivo/produzione effettiva</h4>
                            <div class="chart-container" style="position: relative; height: 100%; width: 100%">
                                <canvas id="chart-production" class="chart chart-bar"
                                        chart-data="dataProduction" chart-labels="labelsProduction" chart-options="barChartOptions" chart-colors="barChartColors">
                                </canvas>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <h4 class="text-center mb-3" style="color: #00624a; font-weight: 500">Rapporto budget/premi erogati</h4>
                            <div class="chart-container" style="position: relative; height: 100%; width: 100%">
                                <canvas id="chart-budget" class="chart chart-bar"
                                        chart-data="dataPrize" chart-labels="labelsPrize" chart-options="barChartOptions" chart-colors="barChartColors">
                                </canvas>
                            </div>
                        </div>
                    </div>

                </div>

            </div>

        </div>
    </div>

</div>
