<div class="row">
    <div class="col-sm-4" ng-if="showAreaSelect">
        <div class="form-group mb-0">
            <select class="form-control" id="areaFilter" ng-model="selectedArea" ng-change="onFilterChange(selectedArea, 'area')" ng-options="area as area.nome for area in areas track by area.id"></select>
        </div>
    </div>
    <div class="col-sm-4" ng-if="showDistrictSelect">
        <div class="form-group mb-0">
            <select class="form-control" id="districtFilter" ng-model="selectedDistrict" ng-change="onFilterChange(selectedDistrict, 'district')" ng-options="district as district.nome for district in districts"></select>
        </div>
    </div>
    <div class="col-sm-4">
        <div class="form-group mb-0">
            <select class="form-control" id="agencyFilter" ng-model="selectedAgency" ng-change="onFilterChange(selectedAgency, 'agency')" ng-options="agency as ( ( agency.id ? agency.id + ' - ' : '') + agency.nome) for agency in agencies"></select>
        </div>
    </div>
</div>