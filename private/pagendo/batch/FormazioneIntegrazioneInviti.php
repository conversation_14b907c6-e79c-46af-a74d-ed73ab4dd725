<?php namespace pagendo\batch;


use com\metadigit\batch\org;
use org\metadigit\console\Request;
use org\metadigit\console\Response;
use org\metadigit\db\ActiveRecordException;
use pagendo\model\formaz\Corso;
use pagendo\model\formaz\IscrizioneManager;
use pagendo\model\User;
use pagendo\util\csv\AbstractCsvParser;

class FormazioneIntegrazioneInviti extends \com\metadigit\batch\AbstractBatch {

	protected $pdo;

	protected $file;

	function run(Request $Req, Response $Res) {
		$directory = rtrim(\org\metadigit\UPLOAD_DIR, '/');
		$parser = AbstractCsvParser::getInstance();
		$parser->setDelimiter(';')->setParseMode(AbstractCsvParser::$CSV_PARSE_SKIP_ON_ERROR);
		list($this->file) = $parser->getFiles($directory, "/^FORMAZ_INTEGRAZIONE_INVITI_[0-9]{8}\.csv$/");

		if(empty($this->file)) {
			$this->log("No file matching pattern.");
			return;
		}

		// Colonne necessarie
		// [0]: id corso
		// [1]: id utente
		$result = $parser->setColumns(2)->parse($this->file);

		$this->pdo->beginTransaction();

		$errors = [];
		$corso = Corso::fetch($result[0][0]);

		if ( empty($corso) ) {
			$this->log("Corso non trovato: {$result[0][0]}");
			return;
		}

		$count = 0;
		foreach ($result as $row) {
			$user = User::fetch($row[1]);
			if ( empty($user) ) {
				$this->log("Utente non trovato: {$row[1]}");
				return;
			}

			try {
				IscrizioneManager::insertInvite([$user], $corso);
				$this->log("User $user->id registrato");
			} catch (ActiveRecordException $ex) {
				$this->log("Line $count: " . $ex->getMessage());
				if ($user) {
					$errors[] = "Line $count: {$user->id} {$user->agenzia_id} {$user->cognome} {$user->cognome} potrebbe essere già iscritto.";
				}
			} catch (\Execption $ex) {
				$this->log("Line $count: " . $ex->getMessage());
				return $this->close();
			}
			$count++;
		}

		foreach ($errors as $error) {
			$this->log($error);
		}

		$this->pdo->commit();
		$this->close();
	}
	protected function close() {
		unlink($this->file);
		$this->log($this->file . " eliminato");
	}
}