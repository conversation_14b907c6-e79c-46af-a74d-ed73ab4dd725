<?php
namespace pagendo\batch;

use pagendo\model\User;
use pagendo\model\formaz\MailManager;
use org\metadigit\core\Core,
    org\metadigit\console\Request,
    org\metadigit\console\Response;

class FormazioneReminderVerifica extends \com\metadigit\batch\AbstractBatch {
    public $mailManager;
    function run(Request $Req, Response $Res) {
        $criteria[] = "active = 1";
        $criteria[] = "type in ('AREAMGR', 'AREAMGR_COLLAB')";
        $recipients = User::fetchAllBy($criteria);

        if (empty($recipients)) {
            $this->log("No users found.");
            $message = $this->mailManager->plainMessage();
            $message->subject('Errore reminder verifiche formazione');
            $this->mailManager->sendMessagePlain($message, array('<EMAIL>' => 'Me'));
            return;
        }

        $success = array();
        $failure = array();
        $this->log(count($recipients) . " destinatari");
        $appUrl = "http://www.portaleagendo.it";

        foreach($recipients as $recipient) {
            $this->log("Processing $recipient->id $recipient->cognome $recipient->nome non ha un indirizzo email valido ($recipient->email).");
            $email = $recipient->email;
            if (empty($email)) {
                $error = "L'utente $recipient->id $recipient->cognome $recipient->nome non ha un indirizzo email valido ($recipient->email).";
                $failure[] = $error;
                $this->log($error);
                continue;
            }


            $message = $this->mailManager->plainMessage();
            $message->setSubject('PortaleAgendo: reminder verifiche Formazione');
            $message->setFrom(array('<EMAIL>'=>'ROBOT PortaleAgendo'));
            $message->setContentType("text/html");
            $message->setBody("Gentile Utente, <br> Le ricordiamo che è necessario accedere all'applicativo Formazione per
                               effettuare le verifiche a campione necessarie.<br/>
                               Può accedere all'applicazione cliccando su <a href='$appUrl'>questo link</a>.<br>
                               Se il link non dovesse funzionare può copiare ed incollare il seguentte
                               indirizzo nella barra degli indirizzi del browser: $appUrl
                               <br><br>Cordiali Saluti.");
            try {
                $this->mailManager->sendMessagePlain($message, array($recipient->email));
                $success[] = "$recipient->email $recipient->cognome $recipient->nome";
            } catch (\Exception $ex) {
                $failure[] = "$recipient->email $recipient->cognome $recipient->nome";
                $this->log("Error: $recipient->email $recipient->cognome $recipient->nome");
            }
        }

        $body = array();
        $body[] = count($recipients) . " utenti";
        $body[] = count($success) . " esiti positivi";
        $body[] = count($failure) . " esiti negativi";
        $body[] = "====================";
        $body[] = implode("<br>", $failure);
        $message = $this->mailManager->plainMessage();
        $message->setSubject("PortaleAgendo: report invio reminder verifiche.")
                ->setBody(implode("<br>", $body))
                ->setFrom(array('<EMAIL>'=>'ROBOT PortaleAgendo'))
                ->setContentType("text/html");

        $this->mailManager->sendMessagePlain($message, array('<EMAIL>' => 'Me'));
    }
}