<?php
namespace pagendo\model\iniz\i7;

class StatusIntermediario extends \org\metadigit\db\ActiveRecord {
	const DB = 0;
	const TABLE = 'vw_iniz_7';
	protected $_data_db = array(
		'user_id'=>null,
		'nome'=>'',
		'cognome'=>'',
		'codEsazione'=>'',
		'agenzia_id'=>null,
		'area'=>'',
		'district'=>'',
		'localita'=>'',
		'pezziVita'=>0,
		'premiVita'=>0,
		'pezziDanni'=>0,
		'premiDanni'=>0,
		'puntiTotali'=>0,
		'puntiSpesi'=>0,
		'puntiDisponibili'=>0
	);
	protected $_data = array(
		'_premiVita'=>null,
		'_premiDanni'=>null
	);
	static protected $keys = array('user_id');

	protected function onInit(){
		$this->_premiVita = number_format($this->premiVita,2,',','.');
		$this->_premiDanni = number_format($this->premiDanni,2,',','.');
	}

	static protected function onBeforeFetch(array &$criteria=null, array &$options=null, $mode=\PDO::FETCH_CLASS) {
		$User = $_SESSION['User'];
		switch($User->type){
			case 'AREAMGR':
			case 'FOR':
			case 'ASV':
				$criteria[]=array('area','=',$User->area);
				break;
			case 'DISTRICTMGR':
				$criteria[]=array('area','=',$User->area);
				$criteria[]=array('district','=',$User->district);
				break;
		}
		return true;
	}

// ######################### INSTANCE #############################################################

// ######################### STATIC ###############################################################

	static function fetchSummary() {
		$sql = <<<SQL
			SELECT
				`area`,
				`district`,
				COUNT(*) AS numIntermediari,
				SUM(`pezziVita`) AS pezziVita,
				SUM(`premiVita`) AS premiVita,
				SUM(`pezziDanni`) AS pezziDanni,
				SUM(`premiDanni`) AS premiDanni,
				SUM(`puntiTotali`) AS puntiTotali,
				SUM(`puntiSpesi`) AS puntiSpesi,
				SUM(`puntiDisponibili`) AS puntiDisponibili
			FROM
				`vw_iniz_7`
			GROUP BY
				`area`, `district`
			WITH ROLLUP
SQL;
		$User = $_SESSION['User'];
		switch($User->type){
			case 'AREAMGR':
			case 'FOR':
			case 'ASV':
				$sql .= ' HAVING area = '.$User->area;
				break;
			case 'DISTRICTMGR':
				$sql .= ' HAVING area = '.$User->area;
				$sql .= ' AND district = '.$User->district;
				break;
		}
		return self::query(__FUNCTION__, $sql)->fetchAll(\PDO::FETCH_ASSOC);
	}
}