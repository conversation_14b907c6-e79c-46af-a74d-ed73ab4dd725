<?xml version="1.0" encoding="UTF-8"?>
<context namespace="api.apps.formazione">
	<includes>
		<include namespace="data"/>
		<include namespace="data.apps.formazione"/>
		<include namespace="data.apps.ivass"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="api.apps.formazione.Dispatcher" class="metadigit\core\web\Dispatcher">
			<properties>
				<property name="defaultViewEngine">json</property>
				<property name="routes" type="array">
					<item key="/utils/*">api.apps.formazione.UtilityController</item>
					<item key="/users/*">api.apps.formazione.UserController</item>
					<item key="/agencies/*">api.apps.formazione.AgencyController</item>
					<item key="/locations/*">api.apps.formazione.LocationController</item>
					<item key="/course/*">api.apps.formazione.CourseController</item>
					<item key="/classroom/*">api.apps.formazione.ClassRoomController</item>
					<item key="/attendance/*">api.apps.formazione.AttendanceController</item>
					<item key="/certificate/*">api.apps.formazione.CertificateController</item>
					<item key="/file/*">api.apps.formazione.FileController</item>
					<item key="/notice/*">api.apps.formazione.NoticeController</item>
					<item key="/dashboard/*">api.apps.formazione.DashboardController</item>
					<item key="/booking/*">api.apps.formazione.BookingController</item>
					<item key="/excel/*">api.apps.formazione.ExcelController</item>
					<item key="/self-certificated/*">api.apps.formazione.SelfCertificatedCreditsController</item>
					<item key="/external/*">api.apps.formazione.ExternalCreditsController</item>
					<item key="/ivass/*">api.apps.formazione.IvassController</item>
					<item key="/verification/*">api.apps.formazione.VerificationController</item>
					<item key="/elearning-legacy/*">api.apps.formazione.ElearningLegacyController</item>
					<item key="/assinform/*">api.apps.formazione.AssinformController</item>
					<item key="/reports/*">api.apps.formazione.ReportController</item>
					<item key="/products/*">api.apps.formazione.ProductController</item>
					<item key="/neofiti/*">api.apps.formazione.NeofitiController</item>
					<item key="/survey/*">api.apps.formazione.SurveyController</item>
					<item key="/*">api.apps.formazione.FormazioneController</item>
				</property>
				<property name="resourcesDir">${BASE_DIR}api/apps/formazione/tpl/</property>
			</properties>
		</object>
		<!-- Controllers -->
		<object id="api.apps.formazione.FormazioneController" class="api\apps\formazione\FormazioneController"></object>
		<object id="api.apps.formazione.CourseController" class="api\apps\formazione\CourseController">
			<properties>
				<property name="manager" type="object">api.apps.formazione.CourseManager</property>
				<property name="repository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="classroomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="charsetConversion" type="object">data.apps.formazione.Utils.CharsetConversion</property>
				<property name="mailer" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="fileRepository" type="object">data.apps.formazione.Repositories.FileRepository</property>
				<property name="recipientRepository" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="noticeRepository" type="object">data.apps.formazione.Repositories.NoticeRepository</property>
				<property name="noticeManager" type="object">api.apps.formazione.NoticeManager</property>
				<property name="courseByAgencyRepo" type="object">data.apps.formazione.Repositories.CourseByAgencyRepository</property>
				<property name="eLearningFetchStatusRepository" type="object">data.apps.formazione.Repositories.ELearningFetchStatusRepository</property>
				<property name="utilities" type="object">data.apps.formazione.Utils.Utilities</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ClassRoomController" class="api\apps\formazione\ClassRoomController">
			<properties>
				<property name="manager" type="object">api.apps.formazione.ClassRoomManager</property>
				<property name="repository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="charsetConversion" type="object">data.apps.formazione.Utils.CharsetConversion</property>
				<property name="areaClassesRepository" type="object">data.apps.formazione.Repositories.AreaClassRoomRepository</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="reminderManager" type="object">api.apps.formazione.ReminderManager</property>
				<property name="attendanceAreaMngRepository" type="object">data.apps.formazione.Repositories.AttendanceAreaMngRepository</property>
				<property name="files" type="object">data.apps.formazione.Repositories.FileRepository</property>
			</properties>
		</object>
        <object id="api.apps.formazione.UserController" class="api\apps\formazione\UserController">
            <properties>
                <property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
                <property name="recipients" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="advancedSearchManager" type="object">api.apps.formazione.Managers.AdvancedSearchManager</property>
				<property name="userManager" type="object">api.apps.formazione.Managers.UserManager</property>
            </properties>
        </object>
		<object id="api.apps.formazione.AgencyController" class="api\apps\formazione\AgencyController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.AgencyRepository</property>
				<property name="creditsRepository" type="object">data.apps.formazione.Repositories.CreditsRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.LocationController" class="api\apps\formazione\LocationController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="manager" type="object">api.apps.formazione.LocationManager</property>
			</properties>
		</object>
		<object id="api.apps.formazione.AttendanceController" class="api\apps\formazione\AttendanceController">
			<properties>
				<property name="manager" type="object">api.apps.formazione.AttendanceManager</property>
				<property name="repository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="creditsRepository" type="object">data.apps.formazione.Repositories.CreditsRepository</property>
				<property name="selfCertificatedCreditsUsersRepository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsUsersRepository</property>
				<property name="externalCreditsRepository" type="object">data.apps.formazione.Repositories.ExternalCreditsRepository</property>
				<property name="selfCertificatedCreditsRepository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsRepository</property>
				<property name="classRoomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="areaClassRoomRepository" type="object">data.apps.formazione.Repositories.AreaClassRoomRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="pdfManager" type="object">data.apps.formazione.Managers.PDFManager</property>
				<property name="advancedSearchRepository" type="object">data.apps.formazione.Repositories.AdvancedSearchRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.CertificateController" class="api\apps\formazione\CertificateController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.CertificateRepository</property>
				<property name="pdfManager" type="object">data.apps.formazione.Managers.PDFManager</property>
				<property name="mailerManager" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="classroomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="certificateMail" type="object">data.apps.formazione.Repositories.AreaCertificateMailRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.FileController" class="api\apps\formazione\FileController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.FileRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.NoticeController" class="api\apps\formazione\NoticeController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.NoticeRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.UtilityController" class="api\apps\formazione\UtilityController">
			<properties>
				<property name="regionRepository" type="object">data.apps.formazione.Repositories.RegionRepository</property>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="companyCredits" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="externalCredits" type="object">data.apps.formazione.Repositories.ExternalCreditsRepository</property>
				<property name="selfCertificates" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.DashboardController" class="api\apps\formazione\DashboardController">
			<properties>
				<property name="manager" type="object">api.apps.formazione.Managers.DashboardManager</property>
				<property name="statsService" type="object">service.StatsService</property>
			</properties>
		</object>
		<object id="api.apps.formazione.BookingController" class="api\apps\formazione\BookingController">
			<properties>
				<property name="users" type="object">data.UsersRepository</property>
				<property name="classrooms" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="manager" type="object">api.apps.formazione.Managers.BookingManager</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="statsService" type="object">service.StatsService</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ExcelController" class="api\apps\formazione\ExcelController">
			<properties>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="locationRepository" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="recipientRepository" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="verificationRepository" type="object">data.apps.formazione.Repositories.VerificationRepository</property>
				<property name="charsetConversion" type="object">data.apps.formazione.Utils.CharsetConversion</property>
			</properties>
		</object>
		<object id="api.apps.formazione.SelfCertificatedCreditsController" class="api\apps\formazione\SelfCertificatedCreditsController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsRepository</property>
				<property name="managerUsers" type="object">api.apps.formazione.Managers.SelfCertificatedCreditsUsersManager</property>
				<property name="repositoryUsers" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsUsersRepository</property>
				<property name="utilities" type="object">data.apps.formazione.Utils.Utilities</property>
				<property name="verifications" type="object">data.apps.formazione.Repositories.VerificationRepository</property>
				<property name="statsService" type="object">service.StatsService</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ExternalCreditsController" class="api\apps\formazione\ExternalCreditsController">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.ExternalCreditsRepository</property>
				<property name="dashboardManager" type="object">api.apps.formazione.Managers.DashboardManager</property>
				<property name="utilities" type="object">data.apps.formazione.Utils.Utilities</property>
				<property name="verifications" type="object">data.apps.formazione.Repositories.VerificationRepository</property>
				<property name="statsService" type="object">service.StatsService</property>
			</properties>
		</object>
		<object id="api.apps.formazione.IvassController" class="api\apps\formazione\IvassController">
			<properties>
				<property name="manager" type="object">api.apps.formazione.Managers.IvassManager</property>
				<property name="charsetConversion" type="object">data.apps.formazione.Utils.CharsetConversion</property>
			</properties>
		</object>
		<object id="api.apps.formazione.VerificationController" class="api\apps\formazione\VerificationController">
			<properties>
				<property name="verifications" type="object">data.apps.formazione.Repositories.VerificationRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ElearningLegacyController" class="api\apps\formazione\ElearningLegacyController"></object>
		<object id="api.apps.formazione.AssinformController" class="api\apps\formazione\AssinformController">
			<properties>
				<property name="assinformEmail" type="object">data.apps.formazione.Repositories.AssinformEmailRepository</property>
				<property name="mailer" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="userData" type="object">data.UsersRepository</property>
				<property name="agencies" type="object">data.apps.formazione.Repositories.AgencyRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.NeofitiController" class="api\apps\formazione\NeofitiController">
			<properties>
				<property name="mailer" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="agencies" type="object">data.apps.formazione.Repositories.AgencyRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ReportController" class="api\apps\formazione\ReportController">
			<properties>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="attendances" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="classrooms" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ProductController" class="api\apps\formazione\ProductController">
			<properties>
				<property name="productRepository" type="object">data.apps.formazione.Repositories.ProductRepository</property>
				<property name="charsetConversion" type="object">data.apps.formazione.Utils.CharsetConversion</property>
				<property name="usersRepository" type="object">data.UsersRepository</property>
        <property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
        <property name="recipients" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="userManager" type="object">api.apps.formazione.Managers.UserManager</property>
			</properties>
		</object>
		<object id="api.apps.formazione.SurveyController" class="api\apps\formazione\SurveyController">
			<properties>
				<property name="surveys" type="object">data.apps.formazione.Repositories.SurveyRepository</property>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
			</properties>
		</object>

		<!-- Managers -->
		<object id="api.apps.formazione.CourseManager" class="TrainingDev\Managers\CourseManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="recipientRepository" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="classRoomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="mailerManager" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="locationRepository" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ClassRoomManager" class="TrainingDev\Managers\ClassRoomManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="attendances" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="users" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="locations" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="courses" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="mailer" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="noticeRepository" type="object">data.apps.formazione.Repositories.NoticeRepository</property>
				<property name="classRooms" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.AttendanceManager" class="TrainingDev\Managers\AttendanceManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="classRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.LocationManager" class="TrainingDev\Managers\LocationManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="classRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.NoticeManager" class="TrainingDev\Managers\NoticeManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.NoticeRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.ReminderManager" class="TrainingDev\Managers\ReminderManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.ReminderRepository</property>
				<property name="mailer" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="classRoomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.DashboardManager" class="data\apps\formazione\Managers\DashboardManager">
			<properties>
				<property name="classroomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="externalCreditsRepository" type="object">data.apps.formazione.Repositories.ExternalCreditsRepository</property>
				<property name="selfCertifiedCreditsRepository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.BookingManager" class="data\apps\formazione\Managers\BookingManager">
			<properties>
				<property name="recipientRepository" type="object">data.apps.formazione.Repositories.RecipientRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="classroomRepository" type="object">data.apps.formazione.Repositories.ClassRoomRepository</property>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="userRepository" type="object">data.apps.formazione.Repositories.UserRepository</property>
				<property name="locationRepository" type="object">data.apps.formazione.Repositories.LocationRepository</property>
				<property name="classroom" type="object">api.apps.formazione.ClassRoomManager</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.SelfCertificatedCreditsManager" class="data\apps\formazione\Managers\SelfCertificatedCreditsManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.SelfCertificatedCreditsUsersManager" class="data\apps\formazione\Managers\SelfCertificatedCreditsUsersManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.SelfCertificatedCreditsUsersRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.AdvancedSearchManager" class="data\apps\formazione\Managers\AdvancedSearchManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.AdvancedSearchRepository</property>
				<property name="repositoryNoYear" type="object">data.apps.formazione.Repositories.AdvancedSearchNoYearRepository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.IvassManager" class="data\apps\formazione\Managers\IvassTableManager">
			<properties>
				<property name="attendanceRepository" type="object">data.apps.formazione.Repositories.AttendanceRepository</property>
				<property name="courseRepository" type="object">data.apps.formazione.Repositories.CourseRepository</property>
				<property name="ivass2Repository" type="object">data.apps.formazione.Repositories.Ivass2Repository</property>
				<property name="ivass3Repository" type="object">data.apps.formazione.Repositories.Ivass3Repository</property>
				<property name="tableIvass23BancheRepository" type="object">data.apps.ivass.Banche23Repository</property>
			</properties>
		</object>
		<object id="api.apps.formazione.Managers.UserManager" class="data\apps\formazione\Managers\UserManager">
			<properties>
				<property name="repository" type="object">data.apps.formazione.Repositories.UserRepository</property>
			</properties>
		</object>
	</objects>
	<events>
		<event name="dispatcher:controller">
			<listeners>
				<listener>service.AuthService->fixture</listener>
				<listener>system.SessionManager->start</listener>
				<listener>service.AuthService->checkAuth</listener>
				<listener>service.AuthService->checkAreaMngDep</listener>
			</listeners>
		</event>
		<event name="dispatcher:view">
			<listeners>
				<listener>system.SessionManager->end</listener>
			</listeners>
		</event>
	</events>
</context>
