<?xml version="1.0" encoding="UTF-8"?>
<context namespace="api.apps.neo">
	<includes>
		<include namespace="data"/>
		<include namespace="data.apps.neo"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="api.apps.neo.Dispatcher" class="metadigit\core\web\Dispatcher">
			<properties>
				<property name="defaultViewEngine">json</property>
				<property name="routes" type="array">
					<item key="/*">api.apps.neo.NeoController</item>
				</property>
				<property name="resourcesDir">${BASE_DIR}api/apps/neo/tpl/</property>
			</properties>
		</object>
		<!-- controllers -->
		<object id="api.apps.neo.NeoController" class="api\apps\neo\NeoController">
			<properties>
				<property name="docs" type="object">data.apps.neo.DocumentsRepository</property>
				<property name="accesses" type="object">data.apps.neo.AccessRepository</property>
				<property name="viewAccesses" type="object">data.apps.neo.ViewAccessRepository</property>
				<property name="statsService" type="object">service.StatsService</property>
			</properties>
		</object>
	</objects>
	<events>
		<event name="dispatcher:controller">
			<listeners>
				<listener>service.AuthService->fixture</listener>
				<listener>system.SessionManager->start</listener>
				<listener>service.AuthService->checkAuth</listener>
				<listener>service.AuthService->checkAreaMngDep</listener>
			</listeners>
		</event>
		<event name="dispatcher:view">
			<listeners>
				<listener>system.SessionManager->end</listener>
			</listeners>
		</event>
	</events>
</context>
