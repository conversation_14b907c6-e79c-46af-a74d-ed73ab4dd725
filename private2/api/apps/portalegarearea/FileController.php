<?php

namespace api\apps\portalegarearea;


use metadigit\core\http\Request;
use metadigit\core\http\Response;
use metadigit\core\Exception;
use Flow\Config;
use Flow\File;
use data\apps\portalegarearea\Repositories\FileRepository;

class FileController extends \metadigit\core\web\controller\ActionController
{
    const FILES_DIR = 'apps/portalegarearea/files';
    const TMP_DIR   = "/srv/portaleagendo.it/data/tmp/";

    /**
     * @var FileRepository
     */
    protected $repository;

    /**
     * @return File
     */
    protected function getFileConfig()
    {
        if (!is_dir(\metadigit\core\TMP_DIR.'flowjs-chunks')) {
            mkdir(\metadigit\core\TMP_DIR.'flowjs-chunks');
        }

        $config = new Config();
        $config->setTempDir(\metadigit\core\TMP_DIR.'flowjs-chunks');
        return new File($config);
    }

    /**
     * Upload files
     *
     * @routing(method="GET", pattern="/upload")
     * @param Request $Req
     * @param Response $Res
     * @return int
     */
    public function getUploadAction(Request $Req, Response $Res)
    {
        $file = $this->getFileConfig();
        if (!$file->checkChunk()) {
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $file->checkChunk() fail');
            return http_response_code(204);
        }
        $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $file->checkChunk() OK');
        return http_response_code(200);
    }

    /**
     * Upload files
     *
     * @routing(method="POST", pattern="/upload")
     * @param Request $Req
     * @param Response $Res
     * @return int
     * @throws \Flow\exception
     */
    public function postUploadAction(Request $Req, Response $Res)
    {
        $initiativeId = $Req->get('initiativeId');
        $filename = $Req->get('flowFilename');

        $file = $this->getFileConfig();

        if (!$file->validateChunk()) {
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs INVALID CHUNCK, retry!');
            // error, invalid chunk upload request, retry
            return http_response_code(400);
        }

        $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $file->saveChunk()');
        $file->saveChunk();

        if ($file->validateFile()) {
            // File upload was completed
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs: file upload was completed');
            $dir = DOWNLOAD_DIR.self::FILES_DIR.'/initiative-'.$initiativeId.'/';
            $path = $dir . time() . $this->sanitizeFilename($filename);

            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            $file->save($path);

            // Save in database
            $result = $this->repository->save($path, $filename, $initiativeId);

            $Res->set('data', $result->json())
                ->setView("json:");
        }

        $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs: not a final chunk, continue to upload');
    }

    /**
     * Delete file
     *
     * @routing(method="DELETE", pattern="/<id>/delete")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     */
    public function deleteAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        try {
            if (! $file = $this->repository->fetch($id)) {
                throw new Exception("The file isn't exists");
            }

            if (! unlink($file->path)) {
                throw new Exception("Cannot remove this file from disk");
            }

            $result = $this->repository->delete($id);

            $success = true;
            $Res->set('data', $result);
        } catch(Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    private function sanitizeFilename($filename)
    {
        $special_chars = array( '?', '[', ']', '/', '\\', '=', '<', '>', ':', ';', ',', "'", '"', '&', '$', '#', '*', '(', ')', '|', '~', '`', '!', '{', '}', '%', '+', chr( 0 ) );

        $filename = preg_replace( "#\x{00a0}#siu", ' ', $filename );
        $filename = str_replace( $special_chars, '', $filename );
        $filename = str_replace( array( '%20', '+' ), '-', $filename );
        $filename = preg_replace( '/[\r\n\t -]+/', '-', $filename );
        $filename = trim( $filename, '.-_' );

        return $filename;
    }
}
