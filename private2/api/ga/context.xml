<?xml version="1.0" encoding="UTF-8"?>
<context namespace="api.ga">
	<includes>
		<include namespace="data"/>
		<include namespace="data.incentivazioni"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="api.ga.Dispatcher" class="metadigit\core\web\Dispatcher">
			<properties>
				<property name="defaultViewEngine">json</property>
				<property name="routes" type="array">
					<item key="/*">api.ga.Controller</item>
				</property>
				<property name="resourcesDir">${BASE_DIR}api/ga/tpl/</property>
			</properties>
		</object>
		<!-- business logic -->

		<!-- controllers -->
		<object id="api.ga.Controller" class="api\ga\Controller">
			<properties>
				<property name="IncentivazioniRepository" type="object">data.incentivazioni.IncentivazioniRepository</property>
			</properties>
		</object>
	</objects>
	<events>
		<event name="dispatcher:controller">
			<listeners>
				<listener>service.AuthService->fixture</listener>
				<listener>system.SessionManager->start</listener>
				<listener>service.AuthService->checkApiAuth</listener>
			</listeners>
		</event>
		<event name="dispatcher:view">
			<listeners>
				<listener>system.SessionManager->end</listener>
			</listeners>
		</event>
	</events>
</context>
