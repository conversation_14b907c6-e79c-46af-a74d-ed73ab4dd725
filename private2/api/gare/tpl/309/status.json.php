<?php
$boxes = [
	[ 'label'=>'OBJ RE',	'value'=>'€ '.number_format($Status['obiettivoRamiElem'], 0, ',','.') ],
	[ 'label'=>'OBJ VITA',	'value'=>'€ '.number_format($Status['obiettivoVitaIndiv'], 0, ',','.') ],
];
switch ($Status['gruppo']) {
	case 1: $palio = '6 viaggi 9 gg per 2 persone, 6 viaggi 7 gg per 2 persone'; break;
	case 2: $palio = '6 viaggi 9 gg per 2 persone, 6 viaggi 7 gg per 2 persone'; break;
	case 3: $palio = '5 viaggi 9 gg per 2 persone, 5 viaggi 7 gg per 2 persone'; break;
	case 4: $palio = '4 viaggi 9 gg per 2 persone, 4 viaggi 7 gg per 2 persone'; break;
	case 5: $palio = '4 viaggi 9 gg per 2 persone, 4 viaggi 7 gg per 2 persone'; break;
	case 6: $palio = '3 viaggi 9 gg per 2 persone, 3 viaggi 7 gg per 2 persone'; break;
}
$metadata = [
	[ 'id'=>'posizione',			'label'=>'Posizione' ],
	[ 'id'=>'agenzia_id',			'label'=>'Agenzia' ],
	[ 'id'=>'localita',				'label'=>'Località' ],
	[ 'id'=>'punti',				'label'=>'Punti' ]
];
