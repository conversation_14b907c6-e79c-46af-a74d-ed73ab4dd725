<?php
namespace pagendo\apps\agt\controller\iniz;
use org\metadigit\web\Request,
	org\metadigit\web\Response,
	pagendo\model\iniz\Polizza,
	pagendo\model\BiliardinoScores7Agt,
	pagendo\model\User,
	pagendo\model\catalogo2009\Premio;

class Iniz07Controller extends \pagendo\apps\agt\controller\AbstractInizController {

	const ID = 7;
	const STATE_ID = 'pagendo.apps.agt.iniz.Iniz07Controller';

	const END_CENSIMENTO_DATE = '2010-04-30';

	static protected $categs = array(
		1=>'Bambino',
		2=>'Beauty e Coccole',
		3=>'Casa e arredo',
		4=>'Cucina',
		5=>'Elettronica',
		6=>'Esperienziale',
		7=>'Hobby e Tempo Libero',
		8=>'Persona',
		9=>'Preziosi',
		10=>'Tavola',
		11=>'Technogym',
		12=>'Thun'
	);
	static protected $points = array(
		1=>'da 130 a 480',
		2=>'da 560 a 720',
		3=>'da 740 a 1020',
		4=>'da 1030 a 1290',
		5=>'da 1330 a 1710',
		6=>'da 1860 a 2250',
		7=>'da 2320 a 2950',
		8=>'da 3000 a 4440',
		9=>'da 4470 a 5990',
		10=>'da 6330 a 10160',
		11=>'da 10640 a 14720',
		12=>'da 15110 a 33560',
		13=>'da 33970 a 56870'
	);
	static protected $pointsQuery = array(
		1=>array(130, 480),
		2=>array(560, 720),
		3=>array(740, 1020),
		4=>array(1030, 1290),
		5=>array(1330, 1710),
		6=>array(1860, 2250),
		7=>array(2320, 2950),
		8=>array(3000, 4440),
		9=>array(4470, 5990),
		10=>array(6330, 10160),
		11=>array(10640, 14720),
		12=>array(15110, 33560),
		13=>array(33970, 56870)
	);

	/** PDO connection
	 * @var org\metadigit\db\PDO */
	protected $pdo;

	function index(Request $Req, Response $Res) {
		$_SESSION['User']->logAccess(self::ID);
		// set Model&View
		$Res->set('menu',	'intro');
		if((date('Y-m-d') > self::END_CENSIMENTO_DATE)) return 'intro2';
		else return 'intro1';
	}

	function gestioneIscritti(Request $Req, Response $Res) {
		$Agenzia = $Res->get('Agente')->getAgenzia();
		$arrayIntermediari = User::fetchAllINTCOntaPunti09($Agenzia->id);
		foreach($arrayIntermediari as $Interm) {
			$Interm->loadStatus(self::ID);
		}
		// set Model&View
		$Res->set('menu',				'gestioneIscritti');
		$Res->set('arrayIntermediari',	$arrayIntermediari);
		return 'gestioneIscritti';
	}

	function gestioneIscrittiCSV(Request $Req, Response $Res) {
		$Agenzia = $Res->get('Agente')->getAgenzia();
		// set Model&View
		$Res->set('class',		'pagendo\model\User');
		$Res->set('fields',		array('agenziaID',	'intermediarioID',	'nome','cognome', 'login','password',	'RUI','puntiTotali','puntiSpesi','puntiDisponibili'));
		$Res->set('labels',		array('ID Agenzia','Cod.Esazione',		'Nome','Cognome', 'Login','Password',	'RUI','Punti TOT','Punti Spesi','Punti Disp.'));
		$Res->set('data',		User::fetchAllINTCOntaPunti09($Agenzia->id));
		$Res->set('filename',	'Intermediari-Iscritti');
		$Res->set('filedate',	date('Y-m-d'));
		return new DAOExcelView('Intermediario');
	}

	function dettaglioPolizze(Request $Req, Response $Res) {
		$Agenzia = $Res->get('Agente')->getAgenzia();
		$params = $this->getSortParams($Req);
		$page = (is_null($Req->page))?1:$Req->page;
		list($arrayPolizze,$totalPages) = Polizza::estrattoAgenzia(self::ID, $Agenzia, $params, $page, self::PAGE_SIZE);
		// set Model&View
		$Res->set('menu',	'dettaglioPolizze');
		$Res->set('currentPage',	$page);
		$Res->set('totalPages',		$totalPages);
		$Res->set('prevPage',		($page==1)?null:$page-1);
		$Res->set('nextPage',		($page==$totalPages)?null:$page+1);
		$Res->set('orderParams',	Polizza::getSortableFields());
		$Res->set('arrayPolizze',	$arrayPolizze);
		$Res->set('polizzeDaAssegnare',	Polizza::countPolizzeDaAssegnare(self::ID,$Agenzia));
		return 'dettaglioPolizze';
	}

	function dettaglioPolizzeDaAssegnare(Request $Req, Response $Res) {
		$Agenzia = $Res->get('Agente')->getAgenzia();
		$params = $this->getSortParams($Req);
		if(empty($params)) $params['idIntermediario'] = 'ASC' ;
		$page = (is_null($Req->page))?1:$Req->page;
		list($arrayPolizze,$totalPages) = Polizza::estrattoPolizzeDaAssegnare(self::ID, $Agenzia, $params, $page, self::PAGE_SIZE);
		// set Model&View
		$Res->set('menu',	'dettaglioPolizze');
		$Res->set('currentPage',	$page);
		$Res->set('totalPages',		$totalPages);
		$Res->set('prevPage',		($page==1)?null:$page-1);
		$Res->set('nextPage',		($page==$totalPages)?null:$page+1);
		$Res->set('orderParams',	Polizza::getSortableFields());
		$Res->set('arrayPolizze',	$arrayPolizze);
		$Res->set('polizzeDaAssegnare',	Polizza::countPolizzeDaAssegnare(self::ID,$Agenzia));
		$Res->set('result',			$Req->result);
		return 'dettaglioPolizzeDaAssegnare';
	}

	function doSubmitPolizze(Request $Req, Response $Res) {
		if(isset($_SESSION['GHOST_AGT'])){
			$Res->sendRedirect('dettaglioPolizzeDaAssegnare?result=N');
			return null;
		}
		$Agenzia = $Res->get('Agente')->getAgenzia();
		// get params
		$capitals=$Req->codice;
		// update DB
		$this->pdo->beginTransaction();
		foreach($capitals as $k=>$idIntermediario){
			$idIntermediario = (!empty($idIntermediario))? str_pad($idIntermediario,5,'0',\STR_PAD_LEFT) : $idIntermediario;
			list($polizza, $ramo, $garanzia) = explode('|',$k);
			$Polizza = Polizza::fetch(self::ID, $Agenzia->id, $polizza, $ramo, $garanzia);
			if($Polizza->idIntermediario != $idIntermediario){
				Polizza::update($Polizza, array('idIntermediario'=>$idIntermediario));
			}
		}
		$this->pdo->commit();
		$this->pdo->prepare('CALL updateI7_set_status()')->execute();
		// no OUTPUT, send REDIRECT
		$Res->sendRedirect('dettaglioPolizzeDaAssegnare?result=Y');
		return null;
	}

	function play(Request $Req, Response $Res) {
		// set Model&View
		$Res->set('menu',	'play');
		return 'play';
	}

	function classificaXML(Request $Req, Response $Res) {
		return $this->biliardinoXML($Req,$Res);
	}

	function biliardinoXML(Request $Req, Response $Res) {
		$Agente = $_SESSION['Agente'];
		$classifica = BiliardinoScores7Agt::selectClassifica();
		$Score = BiliardinoScores7Agt::fetch($Agente->agenziaID);
		// set Model&View
		$Res->set('classifica',	$classifica);
		$Res->set('Score',		$Score);
		header('Content-type: text/xml');
		return 'biliardinoXML';
	}

	function biliardinoSubmit(Request $Req, Response $Res) {
		if(isset($_SESSION['GHOST_AGT'])){
			echo 'false';
			return null;
		}
		$Agente = $_SESSION['Agente'];
		$anonymous = ($Req->anonimo == 'n') ? 'Y':'N';
		BiliardinoScores7Agt::insertScore($Agente, (int)$Req->punti, (int)$Req->tiri, $anonymous);
		// set Model&View
		echo 'true';
		return null;
	}

	function ghostLoginIntermediario(Request $Req, Response $Res) {
		$_SESSION['GHOST_INT'] = User::fetch($Req->id);
		// redirect, NO OUTPUT
		$Res->sendRedirect('/Intermediario/');
		return null;
	}

	function catalogo(Request $Req, Response $Res) {
		$Status = new \StdClass;
		$select = (is_null($Req->select))?1:$Req->select;
		$viewType = (is_null($Req->viewType))?'punteggio':$Req->viewType;
		$page = (is_null($Req->page))?1:$Req->page;
		switch($viewType){
			case 'punteggio':
				$selectionList = self::$points;
				list($premiArray,$totalPages) = Premio::fetchByPunti(self::$pointsQuery[$select][0],self::$pointsQuery[$select][1],$page,3);
				break;
			case 'categoria':
				$selectionList = self::$categs;
				list($premiArray,$totalPages) = Premio::fetchByCategoria($select,$page,3);
				break;
			case 'start':
				$selectionList = self::$points;
				list($premiArray,$totalPages) = Premio::fetchByMin((int)$select,$page,3);
				break;
		}
		// set Model&View
		$Res->set('menu',			'catalogo');
		$Res->set('Status',			$Status);
		$Res->set('currentPage',	$page);
		$Res->set('totalPages',		$totalPages);
		$Res->set('prevPage',		($page==1)?null:$page-1);
		$Res->set('nextPage',		($page==$totalPages)?null:$page+1);
		$Res->set('viewType',		$viewType);
		$Res->set('select',			$select);
		$Res->set('selectionList',	$selectionList);
		$Res->set('premiArray',		$premiArray);
		return 'catalogo';
	}


	/** ---------------------------------------------------------------------------------------------------------
	 * OLD methods -----------------------------------------------------------------------------------
	 */

	function ___elencoIscritti(Request $Req, Response $Res) {
		// set Model&View
		return 'Iniziative/7-ContaPunti/elencoIscritti';
	}

	function ___form(Request $Req, Response $Res) {
		// set Model&View
		return 'Iniziative/7-ContaPunti/form';
	}

	function ___formEdit(Request $Req, Response $Res) {
		// check ACL
		if(isset($_SESSION['Amministratore'])) $Res->sendRedirect('formActionDenied');
		// go on
		$Agenzia = $_SESSION['Agenzia'];
		if(!is_null($Req->id)){
			$_SESSION['IntermediarioEDIT'] = $Intermediario = Intermediario::fetch($Req->id);
			$_SESSION['errors'] = array();
			$_SESSION['codiceErrore'] = 0;
		}elseif(!is_null($Req->new)){
			$_SESSION['IntermediarioEDIT'] = $Intermediario = new Intermediario;
			$_SESSION['errors'] = array();
			$_SESSION['codiceErrore'] = 0;
			$Intermediario->agenziaID = $Agenzia->id;
		}else{
			$Intermediario = $_SESSION['IntermediarioEDIT'];
		}
		// set Model&View
		$Model['Intermediario']	= $Intermediario;
		$Model['errors']	= isset($_SESSION['errors']) ? $_SESSION['errors'] : array();
		$Model['codiceErrore'] = isset($_SESSION['codiceErrore']) ? $_SESSION['codiceErrore'] : 0;
		return 'Iniziative/7-ContaPunti/formEdit';
	}

	function ___doSubmit(Request $Req, Response $Res){
		// store Command into SESSION
		$Intermediario = $_SESSION['IntermediarioEDIT'];
		$Intermediario->idIntermediario = $Req->idIntermediario;
		$Intermediario->nome = strtoupper($Req->nome);
		$Intermediario->cognome = strtoupper($Req->cognome);
		$Intermediario->RUI = strtoupper($Req->RUI);
		$Intermediario->active = $Req->active;
		// verify INPUT
		list($success,$errors,$codiceErrore) = $Intermediario->verify();
		if($success) $success = Intermediario::save($Intermediario);
		$_SESSION['errors'] = $errors;
		$_SESSION['codiceErrore'] = $codiceErrore;
		// NO OUTPUT, send Redirect
		if($success) $Res->sendRedirect('confermaInserimento');
		else $Res->sendRedirect('formEdit');
		return null;
	}

	function ___confermaInserimento(Request $Req, Response $Res) {
		$Intermediario = $_SESSION['IntermediarioEDIT'];
		// set Model&View
		$Model['Intermediario']	= $Intermediario;
		return 'Iniziative/7-ContaPunti/confermaInserimento';
	}

	function ___richiestaCancellazione(Request $Req, Response $Res) {
		// check ACL
		if(isset($_SESSION['Amministratore'])) $Res->sendRedirect('formActionDenied');
		// go on
		$_SESSION['IntermediarioDELETE'] = $Intermediario = Intermediario::fetch($Req->id);
		// set Model&View
		$Model['Intermediario']	= $Intermediario;
		return 'Iniziative/7-ContaPunti/richiestaCancellazione';
	}

	function ___doDelete(Request $Req, Response $Res){
		$Intermediario = $_SESSION['IntermediarioDELETE'];
		$success = Intermediario::delete($Intermediario);
		//$success = true;
		// NO OUTPUT, send Redirect
		if($success) $Res->sendRedirect('confermaCancellazione');
		else $Res->sendRedirect('richiestaCancellazione');
		return null;
	}

	function ___confermaCancellazione(Request $Req, Response $Res) {
		$Intermediario = $_SESSION['IntermediarioDELETE'];
		// set Model&View
		$Model['Intermediario']	= $Intermediario;
		return 'Iniziative/7-ContaPunti/confermaCancellazione';
	}

	function ___formActionDenied(Request $Req, Response $Res) {
		// set Model&View
		return 'Iniziative/7-ContaPunti/formActionDenied';
	}
}