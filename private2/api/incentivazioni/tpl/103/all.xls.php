<?php
$columns[] = ['ID Agenzia',			'agenzia_id'];
$columns[] = ['Località',			'localita'];
$columns[] = ['Agenzia',			'nome'];
$columns[] = ['Area',				'areaName'];
$columns[] = ['Area Manager',		'areaManager'];
$columns[] = ['District Manager',	'districtManager'];
$columns[] = ['Status',				'status'];

$columns[] = ['Pezzi Dim.Multi.', 	'pezziDimMulti'];
$columns[] = ['Pezzi Dim.Quo.', 	'pezziDimQuo'];
$columns[] = ['Pezzi Dim.Cap.',		'pezziDimCap'];
$columns[] = ['Pezzi TOT', 			'pezziTOTALI'];
$columns[] = ['Pezzi bonus TCM',	'pezziSuperBonusTcm'];

$columns[] = ['Prod Dim.Multi.',	'premiCompDimMulti',	function($v) { return number_format($v,2,',','.'); }];
$columns[] = ['Prod Dim.Multi. UnitLink','premiDimMultiUL',	function($v) { return number_format($v,2,',','.'); }];
$columns[] = ['Prod Dim.Quo.',		'premiCompDimQuo',		function($v) { return number_format($v,2,',','.'); }];
$columns[] = ['Prod Dim.Cap.',		'premiCompDimCap',		function($v) { return number_format($v,2,',','.'); }];
$columns[] = ['Prod TOTALE',		'premiCompTOTALI',		function($v) { return number_format($v,2,',','.'); }];
$columns[] = ['Prod bonus TCM',		'premiCompSuperBonusTcm',function($v) { return number_format($v,2,',','.'); }];

$columns[] = ['Obj',				'obiettivoOK'];
$columns[] = ['Bonus',				'bonusOK'];
$columns[] = ['SuperBonusTCM',		'superbonusTcmOK'];
$columns[] = ['€ TOTALE',			'importoErogTOTALE',	function($v) { return number_format($v,2,',','.'); }];
