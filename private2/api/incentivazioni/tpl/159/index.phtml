<?php
//echo '<pre>'; print_r(get_defined_vars()); echo '</pre>';

// incentivazione base
$perc = [0, 0, 0];
$importoErogDimMultiInv = $importoErogDimQuo = $importoErogDimMultiFree = $importoErogDimMultiTarget = 0;
if($Status->obiettivoOK) {
	if($Status->premiTOTALI>=15000)	$perc = [0.006, 0.010, 0.006, 0.010];
	if($Status->premiTOTALI> 40000)	$perc = [0.008, 0.012, 0.008, 0.012];
	if($Status->premiTOTALI>100000)	$perc = [0.010, 0.014, 0.010, 0.014];
	$importoErogDimMultiInv		= $Status->premiCompDimMultiInv * $perc[0];
	$importoErogDimQuo			= $Status->premiCompDimQuo * $perc[1];
	$importoErogDimMultiFree	= $Status->premiCompDimMultiFree * $perc[2];
	$importoErogDimMultiTarget	= $Status->premiCompDimMultiTarget * $perc[3];
}

// Bonus Unit-Linked
$percBonusUL = 0;
if(($Status->premiCompDimQuo + $Status->frazDimMultiInv_UL + $Status->frazDimMultiFree_UL + $Status->frazDimMultiTarget_UL)>=10000) $percBonusUL = 10;
if(($Status->premiCompDimQuo + $Status->frazDimMultiInv_UL + $Status->frazDimMultiFree_UL + $Status->frazDimMultiTarget_UL)>=30000) $percBonusUL = 20;
if(($Status->premiCompDimQuo + $Status->frazDimMultiInv_UL + $Status->frazDimMultiFree_UL + $Status->frazDimMultiTarget_UL)>=90000) $percBonusUL = 30;

if($Status->obiettivoOK==0) $Status->importoErogTOTALE = 0;
?><!DOCTYPE html>
<html>
<head>
	<script type="text/javascript" src="//<?=$staticHost?>/js/flash.js"></script>
</head>

<body style="border: 0; margin: 0; padding: 0;">
<script language="JavaScript" type="text/javascript">writeFlash('//<?=$staticHost?>/swf/incentivazioni/159.swf?<?=rand()?>', '869', '469', 'FFFFFF','9,0,0,0','','online','1<?php echo
	'&premiCompDimMultiInv='.number_format((int)$Status->premiCompDimMultiInv,0,',','.').
	'&premiCompDimMultiFree='.number_format((int)$Status->premiCompDimMultiFree,0,',','.').
	'&premiCompDimMultiTarget='.number_format((int)$Status->premiCompDimMultiTarget,0,',','.').
	'&premiCompDimQuo='.number_format((int)$Status->premiCompDimQuo,0,',','.').
	'&premiTOTALI='.(int)$Status->premiTOTALI.

	// incentivazione base: premiTOTALI >= OBIETTIVO
	'&obiettivoOK='.(int)$Status->obiettivoOK.
	'&importoErogDimMultiInv='.urlencode(number_format($importoErogDimMultiInv,2,',','.')).
	'&importoErogDimQuo='.urlencode(number_format($importoErogDimQuo,2,',','.')).
	'&importoErogDimMultiFree='.urlencode(number_format($importoErogDimMultiFree,2,',','.')).
	'&importoErogDimMultiTarget='.urlencode(number_format($importoErogDimMultiTarget,2,',','.')).
	'&importoErog='.urlencode(number_format($Status->importoErog,2,',','.')).

	// Bonus Unit-Linked: premiCompDimQuo + frazDimMultiInv_UL + frazDimMultiFree_UL + frazDimMultiTarget_UL >= 90000/30000/10000
	'&bonusUnitLinkedOK='.(int)$Status->bonusUnitLinkedOK.
	'&premiUL='.urldecode(str_replace(',','.',($Status->frazDimMultiInv_UL + $Status->frazDimMultiFree_UL + $Status->frazDimMultiTarget_UL + $Status->premiCompDimQuo))).
	'&frazDimMultiInv_UL='.number_format($Status->frazDimMultiInv_UL,0,',','.').
	'&frazDimMultiFree_UL='.number_format($Status->frazDimMultiFree_UL,0,',','.').
	'&frazDimMultiTarget_UL='.number_format($Status->frazDimMultiTarget_UL,0,',','.').
	'&percBonusUL='.urlencode($percBonusUL).
	'&importoErogBonusUnitLinked='.urlencode(number_format($Status->importoErogBonusUnitLinked,2,',','.')).

	// TOTALE (Incentivazione base + Bonus Unit-Linked  // OR // Incentivazione Unit-Linked Pro-Quota)
	'&importoErogTOTALE='.urlencode(number_format($Status->importoErogTOTALE,2,',','.'))
	?>');</script>
</body>
</html>
