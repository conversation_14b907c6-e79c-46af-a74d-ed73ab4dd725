<?php
//echo '<pre>'; print_r(get_defined_vars()); echo '</pre>';
$bonus=0;
if($Status->premiCompDimQuo>=5000) $bonus=10;
if($Status->premiCompDimQuo>=10000) $bonus=20;
?><!DOCTYPE html>
<html>
<head>
	<script type="text/javascript" src="//<?=$staticHost?>/js/flash.js"></script>
</head>

<body style="border: 0; margin: 0; padding: 0;">
<script language="JavaScript" type="text/javascript">writeFlash('//<?=$staticHost?>/swf/incentivazioni/90.swf?<?=rand()?>', '869', '469', 'FFFFFF','9,0,0,0','','online','1<?php echo
	"&capitale=".(int)$Status->premiCompDimCap.
	"&quota=".(int)$Status->premiCompDimQuo.
	"&investimento=".(int)$Status->premiCompDimInv.
	"&free=".(int)$Status->premiCompDimFreeInv.
	"&invFree=".((int)$Status->premiCompDimInv+(int)$Status->premiCompDimFreeInv).
	"&bonus=".$bonus.
	"&incentivoBonus=".$Status->importoErogTOTALE.
	"&incentivo=".$Status->importoErog.
	"&percentuale=".$Status->importoErogBonus.
	"&produzione=".(int)$Status->premiCompTOTALI;
	?>');</script>
</body>
</html>
