<?php

namespace api\utils;


trait MakePaginationData
{
    public function makePaginationData($data, $customFilters = null)
    {
        $page = (isset($data['page']) && $data['page']) ? $data['page'] : null;
        $count = (isset($data['count']) && $data['count']) ? $data['count'] : null;
        $sorting = (isset($data['sorting']) && $data['sorting']) ? json_decode($data['sorting'], true) : null;

        $filter = null;
        if (! $customFilters) {
            $filter = (isset($data['filter']) && $data['filter']) ? json_decode($data['filter'], true) : null;
        }

        $orderExp = null;
        if ($sorting) {
            foreach ($sorting as $key => $value) {
                if (empty($value)) {
                    continue;
                }

                if ( $key === 'approved' ) {
                    $orderExp .= 'approved.'.$value.'|';
                    $orderExp .= 'standard.'.$value.'|';
                    continue;
                }

                $orderExp .= $key.'.'.$value.'|';
            }
            $orderExp = rtrim($orderExp, '|');
        }

        $criteriaExp = null;
        if (! $customFilters) {
            if ($filter) {
                foreach ($filter as $key => $value) {

                    // Eccezione per la ricerca nel backend di MyPage:
                    // quando l'utente inseriva ad esempio '260' per cercare l'agenzia '000260', '260' veniva trattato come un numero "invalidando" la ricerca
                    if ( $key === 'groupama_id' ) {
                        $criteriaExp .= $key.',LIKE,%'.strtoupper($value).'%|';
                        continue;
                    }
                    // Ulteriore eccezione per la ricerca nel backend di MyPage:
                    // quando viene usato il filtro sullo stato di agenzia, passo solo 1 campo (approved) visto che ng-table supporta solo quello.
                    // Però, al momento di formattare il filtro ne applico 2 (uno su approve e uno su standard).
                    // Dentro $value passo un oggetto visto che miracolosamente ng-table me lo permette.
                    /*if ( $key === 'approved' ) {
                        if (!$value) {
                            continue;
                        }
                        $criteriaExp .= "approved,EQ,{$value['approved']}|";
                        $criteriaExp .= "standard,EQ,{$value['standard']}|";
                        continue;
                    }*/

                    if (is_numeric($value)) {
                        $criteriaExp .= "$key,EQ,$value|";
                        continue;
                    }

                    if ($key === 'status' && ($value === 'active' || $value === 'inactive') ) {
                        $criteriaExp .= "$key,EQ,$value|";
                        continue;
                    }

                    if (empty($value)) {
                        continue;
                    }

                    $criteriaExp .= $key.',LIKE,%'.strtoupper($value).'%|';
                }
                $criteriaExp = rtrim($criteriaExp, '|');
            }
        }

        return (object) [
            'page' => $page,
            'pageSize' => $count,
            'orderExp' => $orderExp,
            'criteriaExp' => ($customFilters) ? $customFilters : $criteriaExp
        ];
    }

    public function makeCustomFilter($object, $data)
    {
        $filters = [];
        $result = null;

        if ($data) {
            switch ($object) {
                case 'recipient':
                    if (isset($data['agents']) && $data['agents']) {
                        $filters[] = 'type,EQ,AGENTE';
                    }
                    if (isset($data['intermediariesRegistered']) && $data['intermediariesRegistered']) {
                        $filters[] = 'type,EQ,INTERMEDIARIO|rui,REGEXP,^E';
                    }
                    if (isset($data['employeesRegistered']) && $data['intermediariesRegistered']) {
                        $filters[] = 'type,EQ,INTERMEDIARIO|rui,REGEXP,^E|role,IN,DIP_AGZ,DIP_SUBAGZ';
                    }
                    if (isset($data['employeesUnregistered']) && $data['intermediariesRegistered']) {
                        $filters[] = 'type,EQ,INTERMEDIARIO|rui,NOT REGEXP,^E|role,IN,DIP_AGZ,DIP_SUBAGZ';
                    }
                    break;
            }
        }

        if ($filters) {
            $result = implode('|', $filters);
        }

        return $filters;
    }
}
