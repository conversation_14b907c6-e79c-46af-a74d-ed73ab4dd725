<?php

namespace apps\ui;

use metadigit\core\http\Request,
	metadigit\core\http\Response,
	util\Hosts;

class Controller extends  \metadigit\core\web\controller\ActionController
{

	/**
	 * @routing(pattern="^/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function indexAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index');
	}

	/**
	 * @routing(pattern="^/autorefresh/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function octoAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-octo');
	}

	/**
	 * @routing(pattern="^/formazione/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function formazioneAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-formazione');
	}

	/**
	 * @routing(pattern="^/restart/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function restartAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-restart');
	}

	/**
	 * @routing(pattern="^/besmart/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function besmartAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-besmart');
	}

	/**
	 * @routing(pattern="^/rinnoviamoci/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function rinnoviamociAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-rinnoviamoci');
	}

	/**
	 * @routing(pattern="^/focus/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function focusAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-focus');
	}

	/**
	 * @routing(pattern="^/firstchance/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function firstchanceAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-firstchance');
	}

	/**
	 * @routing(pattern="^/arag/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function aragAction(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-arag');
	}

	/**
	 * @routing(pattern="^/rinnoviamoci-fase-2/")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function rinnoviamociFase2Action(Request $Req, Response $Res)
	{
		$Res->set('staticHost', Hosts::getStaticHost())
			->set('browscap', $_SESSION['_BROWSCAP_'])
			->setView('index-rinnoviamoci-fase2');
	}

    /**
     * @routing(pattern="^/compass/")
     * @param Request  $Req
     * @param Response $Res
     */
    function compassAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-compass');
    }


    /**
     * @routing(pattern="^/fiditalia/")
     * @param Request  $Req
     * @param Response $Res
     */
    function fiditaliaAction(Request $Req, Response $Res)
    {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-fiditalia');
    }

    /**
     * @routing(pattern="^/fullprotection/")
     * @param Request  $Req
     * @param Response $Res
     */
    function fullprotectionAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-fullprotection');
    }

    /**
     * @routing(pattern="^/cms-agenzie/")
     * @param Request  $Req
     * @param Response $Res
     */
    function cmsAgenzieAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-cms-agenzie');
    }

    /**
     * @routing(pattern="^/event-manager/")
     * @param Request  $Req
     * @param Response $Res
     */
    function gaontopAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-event-manager');
    }

    /**
     * @routing(pattern="^/dolce-vita/")
     * @param Request  $Req
     * @param Response $Res
     */
    function dolceVitaAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-dolcevita');
    }

    /**
     * @routing(pattern="^/rinnoviamoci-2023/")
     * @param Request  $Req
     * @param Response $Res
     */
    function rinnoviamoci2023Action(Request $Req, Response $Res)
    {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-rinnoviamoci-2023');
    }

    /**
     * @routing(pattern="^/casa/")
     * @param Request  $Req
     * @param Response $Res
     */
    function casaAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-casa');
    }

    /**
     * @routing(pattern="^/dinamiche/")
     * @param Request  $Req
     * @param Response $Res
     */
    function dinamicheAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-dinamiche');
    }

    /**
     * @routing(pattern="^/infortuni/")
     * @param Request  $Req
     * @param Response $Res
     */
    function infortuniAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-infortuni');
    }

    /**
     * @routing(pattern="^/focus-2023/")
     * @param Request  $Req
     * @param Response $Res
     */
    function focus2023Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-focus-2023');
    }

    /**
     * @routing(pattern="^/supernova/")
     * @param Request  $Req
     * @param Response $Res
     */
    function supernovaAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-supernova');
    }

    /**
     * @routing(pattern="^/focus-2023-fase2/")
     * @param Request  $Req
     * @param Response $Res
     */
    function focus2023Fase2Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-focus-2023-fase2');
    }

    /**
     * @routing(pattern="^/fullprotection-2023/")
     * @param Request  $Req
     * @param Response $Res
     */
    function fullprotection2023Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-fullprotection-2023');
    }

    /**
     * @routing(pattern="^/focus-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function focus2024Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-focus-2024');
    }

    /**
     * @routing(pattern="^/_/")
     * @param Request  $Req
     * @param Response $Res
     */
    function agendo3Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-agendo3');
    }

    /**
     * @routing(pattern="^/supernova-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function supernova2024Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-supernova-2024');
    }

    /**
     * @routing(pattern="^/portalegarearea/")
     * @param Request  $Req
     * @param Response $Res
     */
    function portaleGareAreaAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-portalegarearea');
    }

    /**
     * @routing(pattern="^/fullprotection-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function fullprotection2024Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-fullprotection-2024');
    }

    /**
     * @routing(pattern="^/fullbenessere-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function fullbenessere2024Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-fullbenessere-2024');
    }

    /**
     * @routing(pattern="^/uniqum/")
     * @param Request  $Req
     * @param Response $Res
     */
    function uniqumAction(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-uniqum');
    }

    /**
     * @routing(pattern="^/arag-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function arag2024Action(Request $Req, Response $Res)
    {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-arag-2024');
    }

    /**
     * @routing(pattern="^/focus-2024-fase2/")
     * @param Request  $Req
     * @param Response $Res
     */
    function focus2024Fase2Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-focus-2024-fase2');
    }

    /**
     * @routing(pattern="^/supernova-rf-2024/")
     * @param Request  $Req
     * @param Response $Res
     */
    function supernovaRF2024Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-supernova-rf-2024');
    }

    /**
     * @routing(pattern="^/focus-2025/")
     * @param Request  $Req
     * @param Response $Res
     */
    function focus2025Action(Request $Req, Response $Res) {
        $Res->set('staticHost', Hosts::getStaticHost())
            ->set('browscap', $_SESSION['_BROWSCAP_'])
            ->setView('index-focus-2025');
    }

	/**
	 * @routing(pattern="(.+)")
	 * @param Request  $Req
	 * @param Response $Res
	 */
	function catchallAction(Request $Req, Response $Res)
	{
		$Res->redirect('/');
	}
}
