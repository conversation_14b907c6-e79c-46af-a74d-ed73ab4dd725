<?php
namespace batch;
use batch\utils\anagrafica\Analyzer;
use batch\utils\anagrafica\Matrnx;
use batch\utils\anagrafica\Nxca;
use batch\utils\anagrafica\Renderer;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response;
use org\metadigit\mail\Mailer;

class AnagraficaCheck extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const DISACTIVATION_MONTHS_BY_TYPE = [
	    // 'AGENTE' => 6,
	    'INTERMEDIARIO' => 12
    ];

    /**
     * @var Matrnx
     */
    protected $matrnx;

    /**
     * @var Nxca
     */
    protected $nxca;

    /**
     * @var Analyzer
     */
    protected $analyzer;

    /**
     * @var Renderer
     */
    protected $renderer;

    /**
     * @var bool
     */
    protected $backup;

    /**
     * @var  Mailer
     */
    protected $mailer;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    protected function csv($filename)
    {
        if (! file_exists($filename)) {
            return null;
        }

        if (! $file = file($filename)) {
            return null;
        }

        array_shift($file);

        return array_map(function($line){
            return str_getcsv($line, ";");
        }, $file);
    }

    protected function finalize($filepath, $filename, $output)
    {
        $message = $this->mailer->newMessage();

        $message
            ->addFrom('<EMAIL>')
            ->addTo("<EMAIL>")
            ->addTo("<EMAIL>")
            ->addTo("<EMAIL>")
            ->setSubject("Esito elaborazione $filename")
            ->setBody("In allegato il risultato dell'elaborazione.")
            ->attach(\Swift_Attachment::fromPath($output));

        try {
            $this->mailer->send($message);
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            $this->log($ex->getTraceAsString());
        }

        try {
            rename($filepath . $filename, "{$filepath}processed-".date('Ymd')."-{$filename}");
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            $this->log($ex->getTraceAsString());
        }


        return;
        // @TODO refactor/update


        if (! $this->backup) {
            $this->log("Backup disabled.");
            return null;
        }


        try {
            if (! file_exists(\metadigit\core\BACKUP_DIR . "CSV")) {
                mkdir(\metadigit\core\BACKUP_DIR . "CSV", 0770, true);
            }

            if (! file_exists(\metadigit\core\BACKUP_DIR . "CSV/Anagrafiche")) {
                mkdir(\metadigit\core\BACKUP_DIR . "CSV/Anagrafiche", 0770, true);
            }

            $destination = \metadigit\core\BACKUP_DIR . "CSV/Anagrafiche/" . date('Y-m-d');

            if (! file_exists($destination)) {
                mkdir($destination, 0770, true);
            }

            rename(\metadigit\core\UPLOAD_DIR . $filename, $destination . "/{$filename}");

        } catch (\Exception $ex) {
            $this->log($ex->getTraceAsString());
        }
    }

    /**
     * @batch(description="Anagrafica: check su MATRNX")
     * @param Request $Req
     * @param Response $Res
     */
    function matrnxAction(Request $Req, Response $Res)
    {
        $matrnxPath = UPLOAD_DIR . "anagrafica/";
        $matrnxFile = "MATRNX";

        if (! $csv = $this->csv($matrnxPath . $matrnxFile)) {
            $this->log("File not found");
            return;
        }

        $this->log("ENV: " . \metadigit\core\ENVIRONMENT);

        $statementAgenzie = $this->pdoPrepare("SELECT * FROM vw_agenzie_am_dm_2 where id = :id");

        $result = [
            'OK' => 0,
            'ERR' => 0,
            'W' => 0,
        ];

        $output = [["SEVERITY", "AGENZIA", "SOGGETTO", "VAL.AGENDO", "VAL.GROUPAMA", "NOTE", "RETE GROUPAMA", "STATO GROUPAMA", "STATO AGENDO"]];

        foreach($csv as $row) {
            if (($prog = $this->matrnx->get('prog', $row)) != "000") {
                continue;
            }

            $code = $this->matrnx->code($row);

            $statementAgenzie->execute([':id' => $code]);

            $output = array_merge(
                $output,
                $this->checkAgenzia(
                    $statementAgenzie->fetch(\PDO::FETCH_OBJ),
                    $row,
                    $result
                )
            );

            //if (\metadigit\core\ENVIRONMENT == "PROD") {
                //sleep(0.1);
            //}
        }

        $this->log(print_r($result, 1));

        $outFile = $this->renderer->excel($output, "MATRNX-out-" . date('Ymd'));

        $this->finalize($matrnxPath, $matrnxFile, $outFile);
    }

    protected function checkAgenzia($agenzia, $row, &$result)
    {
        $err = [];
        $warning = [];
        $output = [];

        $status = (string) trim($this->matrnx->get('active', $row));
        $areaCode = (int) trim($this->matrnx->get('area_code', $row));
        $agencyType = (string) trim($this->matrnx->get('type', $row));

        $statementAreaCheck = $this->pdoPrepare("SELECT ga.* FROM `geo_aree` AS ga WHERE ga.id = :areaCode AND ga.status = 'ON'");
        $statementAreaCheck->execute([':areaCode' => $areaCode]);
        $areaCheck = $statementAreaCheck->fetch(\PDO::FETCH_OBJ);

        if (!$agenzia) {
            // Agenzia inesistente
            if ($status === 'AT' && $agencyType === 'AGENZIE GENERALI' && $areaCheck) {
                // Agenzia Attiva, di tipo AGENZIE GENERALI e con area presente e attiva in geo_aree

                // Creazione Agenzia
                if (!$this->createAgency($row)) {
                    $result['ERR']++;
                    $this->log("Agenzia {$this->matrnx->get('agenzia', $row)} non creata!");

                    $output[] = [
                        3,
                        "",
                        'AGENZIA',
                        "",
                        $this->matrnx->get('agenzia', $row),
                        "Agenzia non creata!",
                        $this->matrnx->get('network', $row),
                        $status,
                        "",
                    ];

                    return $output;
                }

                $output[] = [
                    3,
                    $this->matrnx->code($row),
                    'AGENZIA',
                    "",
                    $this->matrnx->get('agenzia', $row),
                    "Agenzia non trovata in Agendo e creata!",
                    $this->matrnx->get('network', $row),
                    $status,
                    "",
                ];

                return $output;
            }

            $result['ERR']++;
            $this->log("Agenzia {$this->matrnx->get('agenzia', $row)} non trovata e non creata perché 
                chiusa o non di tipo AGENZIE GENERALI oppure are non presente o non attiva in geo_aree.");

            $output[] = [
                3,
                "",
                'AGENZIA',
                "",
                $this->matrnx->get('agenzia', $row),
                "Agenzia non trovata in Agendo",
                $this->matrnx->get('network', $row),
                $status,
                "",
            ];

            return $output;
        }

        // @fixme => matrnx
        foreach($row as $key => $col) {
            $row[$key] = strtoupper(trim($col));
        }

        //
        //
        // Status
        //
        //

        if ($agenzia->status !== "OFF" && $status === 'CH') {
            // Disattivazione automatica
            $stmtUpdateStatus = $this->pdoPrepare("UPDATE `agenzie` SET `status` = 'OFF' WHERE id = :id");
            $stmtUpdateStatus->execute([':id' => $agenzia->id]);

            $output[] = [
                3,
                $agenzia->id,
                'AGENZIA',
                $agenzia->status,
                $status,
                "Chiusura automatica agenzia effettuata!",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];

            $err[] = "Errore stato agenzia KA:{$agenzia->status} / GA:" . $status;
        }

        /*$r = $this->analyzer->differ(
            $agenzia->nome,
            $this->matrnx->get('name', $row),
            $agenzia->id,
            "Errore denominazione agenzia"
        );

        if ($r) {
            $output[] = $r->result;
        }*/

        if (trim(strtoupper($agenzia->nome)) != $this->matrnx->get('name', $row)) {
            $output[] = [
                3,
                $agenzia->id,
                'AGENZIA',
                $agenzia->nome,
                $this->matrnx->get('name', $row),
                "Errore denominazione agenzia",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $err[] = "Errore denominazione Agenzia KA:{$agenzia->nome} / GA:" . $this->matrnx->get('name', $row);
        };

        if (
            trim(strtoupper($agenzia->indirizzo)) != $this->matrnx->get('address', $row) ||
            trim(strtoupper($agenzia->localita)) != $this->matrnx->get('location', $row) ||
            trim(strtoupper($agenzia->cap)) != $this->matrnx->get('cap', $row)
        ) {
            $output[] = [
                3,
                $agenzia->id,
                'AGENZIA',
                "{$agenzia->indirizzo} {$agenzia->localita} {$agenzia->cap}",
                $this->matrnx->get('address', $row) . " " . $this->matrnx->get('location', $row). " " . $this->matrnx->get('cap', $row),
                "Errore indirizzo agenzia",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $err[] = "Errore indirizzo Agenzia KA:{$agenzia->indirizzo} {$agenzia->localita} {$agenzia->cap} / GA:" . $this->matrnx->get('address', $row) . " " . $this->matrnx->get('location', $row). " " . $this->matrnx->get('cap', $row);
        }

        //
        //
        // AM/DM
        //
        //
        /*$r = $this->analyzer->differ(
            "{$agenzia->cognomeAM} {$agenzia->nomeAM}",
            $this->matrnx->get('am', $row),
            $agenzia->id,
            "Errore nominativo AM"
        );

        if ($r) {
            $output[] = $r->result;
            $err[] = "Errore nominativo AM KA:{$agenzia->cognomeAM} {$agenzia->nomeAM} / GA:" . $this->matrnx->get('am', $row);
        }*/

        if (trim(strtoupper("{$agenzia->cognomeAM} {$agenzia->nomeAM}")) != $this->matrnx->get('am', $row)) {
            $output[] = [
                3,
                $agenzia->id,
                'AM',
                "{$agenzia->cognomeAM} {$agenzia->nomeAM}",
                $this->matrnx->get('am', $row),
                "Errore di assegnazione agenzia/AM",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $err[] = "Errore nominativo AM KA:{$agenzia->cognomeAM} {$agenzia->nomeAM} / GA:" . $this->matrnx->get('am', $row);
        };

        if ($agenzia->area != $this->matrnx->get('area', $row)) {
            $output[] = [
                1,
                $agenzia->id,
                'AREA',
                $agenzia->area,
                $this->matrnx->get('area', $row),
                "Codice di area non corrispondente",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $warning[] = "Errore codice area KA:{$agenzia->area} / GA:" . $this->matrnx->get('area', $row);
        }

        if (strtoupper("{$agenzia->cognomeDM} {$agenzia->nomeDM}") != $this->matrnx->get('dm', $row)) {
            $output[] = [
                3,
                $agenzia->id,
                'DM',
                "{$agenzia->cognomeDM} {$agenzia->nomeDM}",
                $this->matrnx->get('dm', $row),
                "Errore di assegnazione agenzia/DM",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $err[] = "Errore nominativo DM KA:{$agenzia->cognomeDM} {$agenzia->nomeDM} / GA:" . $this->matrnx->get('dm', $row);
        }

        if ($agenzia->district != $this->matrnx->get('district', $row)) {
            $output[] = [
                1,
                $agenzia->id,
                'DISTRICT',
                $agenzia->district,
                $this->matrnx->get('district', $row),
                "Codice distretto non corrispondente",
                $this->matrnx->get('network', $row),
                $status,
                $agenzia->status,
            ];
            $warning[] = "Errore codice district KA:{$agenzia->district} / GA:" . $this->matrnx->get('district', $row);
        }

        //
        //
        // email
        //
        //

        if (! $err && ! $warning) {
            $result['OK']++;
            $this->log("{$agenzia->id} OK");
        }

        if ($err) {
            $result['ERR']++;
            $this->log("{$agenzia->id} ERR " . implode("|", $err));
        }

        if ($warning) {
            $result['W']++;
            $this->log("{$agenzia->id} W " . implode("|", $warning));
        }

        $this->log("===========================");

        return $output;
    }

    /**
     * @batch(description="Anagrafica: check su NXCA6603")
     * @param Request $Req
     * @param Response $Res
     */
    function nxcaAction(Request $Req, Response $Res)
    {
        // Path per test in locale
        // $nxcaPath = '/storage/portaleagendo.it/data/downloads/apps/anagrafica/';
        $nxcaPath = UPLOAD_DIR . "anagrafica/";
        $nxcaFile = "NXCA6603";

        if (! $csv = $this->csv($nxcaPath . $nxcaFile)) {
            $this->log("File not found");
            return;
        }

        $this->log("ENV: " . \metadigit\core\ENVIRONMENT);

        $page = 0;
        $ipp = 10;

        $res = 0;
        $total = 0;

        $i = 0;

        // INIT
        $this->pdoExec("truncate users_anag_check");

        do {
            try {
                $res = $this->pdoExec("insert into users_anag_check (select * from vw_anag_users limit $page,$ipp)");

                $total += $res;

            } catch (\Exception $ex) {
                $this->log($ex->getTraceAsString());
            }

            $i++;
            $page = $i * $ipp;

            if (\metadigit\core\ENVIRONMENT == 'PROD') {
                sleep(0.1);
            }

        } while ($res);

        $output = $this->processNXCA($csv);

        $outFile = $this->renderer->excel($output, "NXCA6603-out-" . date('Ymd'));

        $this->finalize($nxcaPath, $nxcaFile, $outFile);
    }

    function processNXCA($csv)
    {
        //$statement = $this->pdoPrepare("SELECT * FROM users_anag_check where type ='AGENTE' and agenzia_id=:agenzia_id and (rui = :rui or MATCH(nome) AGAINST (:nome) and MATCH(cognome) AGAINST(:cognome))");
        $statement = $this->pdoPrepare("SELECT * FROM users_anag_check WHERE type ='AGENTE' AND `active` = 1 AND (rui = :rui OR MATCH(nome) AGAINST (:nome) AND MATCH(cognome) AGAINST(:cognome))");
        //$statement = $this->pdoPrepare("SELECT * FROM users_anag_check where type ='AGENTE' and (rui = :rui or MATCH(nome) AGAINST (:nome) and MATCH(cognome) AGAINST(:cognome))");

        $headers = [
            [
                "SEVERITY",
                "AGENZIA GROUPAMA",
                "AGENZIA AGENDO",
                "ID AGENDO",
                "ATTIVAZIONE AGENDO",
                "NOMINATIVO AGENDO",
                "NOMINATIVO GROUPAMA",
                "RILEVAZIONE",
                "NOTE",
                "AZIONE SUGGERITA",
            ]
        ];

        $ids = [];

        foreach($csv as $row) {
            if ($this->nxca->skip($row)) {
                continue;
            }

            $statement->execute([
                ':rui' => $this->nxca->get('rui', $row),
                ':nome' => $this->nxca->get('name', $row),
                ':cognome' => $this->nxca->get('lastname', $row),
                //':agenzia_id' => $this->nxca->code($row),
            ]);

            $output = $this->checkAgenti(
                $statement->fetchAll(\PDO::FETCH_OBJ),
                $row
            );

            $ids = array_merge($ids, $output['ids']);

            $headers = array_merge($headers, $output['logs']);
        }

        $sql = "SELECT u.*, a.status AS agencyStatus FROM users_anag_check u join agenzie a on a.id = u.agenzia_id WHERE u.type ='AGENTE'";
        if (count($ids) > 0) {
            $sql .= " AND u.id NOT IN (".(implode(",", $ids)).")";
        }

        $statement2 = $this->pdoQuery($sql);

        // Agents are not in file
        foreach ($statement2->fetchAll(\PDO::FETCH_OBJ) as $agent) {
            if (!$agent->active) {
                continue;
            }

            // Disattivazione automatica
            if ($agent->active && $agent->agenzia_id != 'G000' && $agent->agencyStatus != 'DIREZ') {
                $stmtUpdateStatus = $this->pdoPrepare("UPDATE `users` SET `active` = 0 WHERE id = :id");
                $stmtUpdateStatus->execute([':id' => $agent->id]);
            }

            $config = $this->analyzer->getConfig()['agents.active.inverse'];

            $headers[] = $this->nxca->log("-", $config, null, $agent);

            $this->log("{$agent->id} {$agent->cognome} {$agent->nome} BAD ACTIVE");
        }

        return $headers;
    }

    protected function checkAgenti($agents, $row)
    {
        $output = [
            'ids' => [],
            'logs' => []
        ];

        $nxName = $this->nxca->get('lastname', $row) . " " . $this->nxca->get('name', $row);
        $nxCode = $this->nxca->code($row);
        $source = compact('nxName', 'nxCode');

        //
        // Not found
        //
        if (!$agents) {
            // Search Intermediario
            $rui = str_replace('A', 'E', $this->nxca->get('rui', $row));
            $statement = $this->pdoPrepare("SELECT * FROM users WHERE type = 'INTERMEDIARIO' AND `active` = 1 AND (rui = :rui OR (LOWER(TRIM(nome)) = :nome AND LOWER(TRIM(cognome)) = :cognome))");
            $statement->execute([
                ':rui' => $rui,
                ':nome' => strtolower(trim($this->nxca->get('name', $row))),
                ':cognome' => strtolower(trim($this->nxca->get('lastname', $row))),
                //':agenzia_id' => $this->nxca->code($row),
            ]);
            $agents = $statement->fetchAll(\PDO::FETCH_OBJ);

            if (!$agents) {
                // Creazione Agente
                if (!$this->createAgent($row)) {
                    $config = $this->analyzer->getConfig()['agents.notCreated'];

                    $output['logs'][] = $this->nxca->log("-", $config, $source);

                    return $output;
                }
            } else {
                $stmtUpdateStatus = $this->pdoPrepare("UPDATE `users` SET `rui` = :rui, `type` = 'AGENTE' WHERE id = :id");
                $stmtUpdateStatus->execute([
                    ':rui' => $this->nxca->get('rui', $row),
                    ':id' => $agents[0]->id
                ]);

                $config = $this->analyzer->getConfig()['agents.updated'];

                $output['logs'][] = $this->nxca->log("-", $config, $source);
            }

            $config = $this->analyzer->getConfig()['agents.notFound'];

            $output['logs'][] = $this->nxca->log("-", $config, $source);

            return $output;
        }

        foreach ($this->analyzer->getConfig() as $checkName => $config) {
            if (! $config['enabled']) {
                continue;
            }

            $this->log("Check $checkName on $nxName");

            $check = $this->analyzer->foo(
                $checkName,
                $agents,
                $row,
                $source
            );

            if ($check->success) {
                continue;
            }

            $this->log("Check failed");

            $output['logs'] = array_merge($output['logs'], array_values($check->logs));
        }

        // Found agents id
        foreach($agents as $agent) {
            $output['ids'][] = $agent->id;
        }

        return $output;
    }

    /**
     * Create the agency
     *
     * @param $row
     * @return bool
     * @throws \Exception
     */
    private function createAgency($row)
    {
        $code = $this->matrnx->code($row);
        $area = $this->matrnx->get('area', $row);
        $district = $this->matrnx->get('district', $row);
        $localita = $this->matrnx->get('location', $row);
        $name = $this->matrnx->get('name', $row);
        $email = $this->matrnx->get('email', $row);
        if (empty($email)) {
            $this->log("Agenzia {$this->matrnx->get('agenzia', $row)} manca l'email!");
            return false;
        }
        $phone = $this->matrnx->phone($row);
        if (empty($phone)) {
            $this->log("Agenzia {$this->matrnx->get('agenzia', $row)} manca il telefono!");
            return false;
        }
        $address = $this->matrnx->get('address', $row);
        $cap = $this->matrnx->get('cap', $row);
        $city = $this->matrnx->get('location', $row);
        $mandatoDate = $this->matrnx->date('man_dt', $row);

        $statementAgenzie = $this->pdoPrepare("SELECT id, provincia_id, regione_id 
                                                     FROM geo_comuni 
                                                    WHERE nome = :location");
        $statementAgenzie->execute([':location' => $city]);
        $locationIds = $statementAgenzie->fetch(\PDO::FETCH_OBJ);

        try {
            $res = $this->pdoExec("
                    INSERT INTO `agenzie` (
                                           `id`, `area`, `district`,
                                           `localita`, `nome`,
                                           `email`, `status`, `telefono`, 
                                           `indirizzo`, `cap`, `citta`,
                                           `comune_id`, `provincia_id`, `regione_id`,
                                           `modelloX`, `modelloY`, `agVinc`, 
                                           `dataMandato`,
                                           `numAgenti`, `numDipAgz`, `numProdAgz`, `numSubagz`
                                          ) 
                         VALUES (
                                 '$code', '$area', '$district',
                                 '$localita', '$name',
                                 '$email', 'ON', '$phone',
                                 '$address', '$cap', '$city',
                                 $locationIds->id, $locationIds->provincia_id, $locationIds->regione_id,
                                 0,0,0, 
                                 '$mandatoDate',
                                 0, 0, 0, 0 
                         )"
            );

            return (bool) $res;
        } catch (\Exception $ex) {
            $this->log($ex->getTraceAsString());
            return false;
        }
    }

    /**
     * Create the agent
     *
     * @param $row
     * @return bool
     */
    private function createAgent($row)
    {
        $code = $this->nxca->code($row);
        $name = $this->nxca->get('name', $row);
        $lastname = $this->nxca->get('lastname', $row);
        $rui = $this->nxca->get('rui', $row);

        try {
            $res = $this->pdoExec("
                    INSERT INTO `users` ( `type`, `active`, `nome`, `cognome`, `email`, `cellulare`, `area`, `district`,
                                           `agenzia_id`, `codEsazione`, `rui`, `piva`, `ruolo`, `acl_elearning`, `isAzienda` ) 
                         VALUES ( 'AGENTE', 0, '$name', '$lastname', '$name.$<EMAIL>' , null, null, null,
                                 '$code', null, '$rui', null, null, 1, 0 )
                   ");

            return (bool) $res;
        } catch (\Exception $ex) {
            $this->log($ex->getTraceAsString());
            return false;
        }
    }

    /**
     * @batch(description="Anagrafica: disattivazione temporanea degli intermediari")
     * @param Request $Req
     * @param Response $Res
     */
    public function temporaryDeactivationAction(Request $Req, Response $Res)
    {
        foreach (self::DISACTIVATION_MONTHS_BY_TYPE AS $type => $months) {
			// Retrieve users
			$statementAgenzie = $this->pdoQuery("SELECT u.`id`
														 FROM `users` u LEFT JOIN `stats_users_actions` s ON s.`user_id` = u.`id` 
														WHERE u.`active` = 1
														  AND u.`type` = '$type' 
														  AND DATE_ADD(u.`createdAt`, INTERVAL $months MONTH) < DATE_FORMAT(NOW(),'%Y-%m-%d') 
														  AND s.`date` IS NULL
														GROUP BY u.`id`");
			// Fetch users
            $userIds = [];
            foreach($statementAgenzie->fetchAll(\PDO::FETCH_OBJ) as $user) {
                $userIds[] = $user->id;
            }
            // Deactivate users
			if (count($userIds) > 0) {
                $stmtUpdateStatus = $this->pdoPrepare("UPDATE `users` SET `active` = 0 WHERE id IN (".implode(",", $userIds).")");
                $stmtUpdateStatus->execute();
            }
		}
    }
}
