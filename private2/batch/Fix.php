<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class Fix extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const FILE_AREE_DISTRICT_AGENZIE = 'FIX_AREE_DISTRICT_AGENZIE_20160330.csv';

	/**
	 * @batch(description="FIX AREE: riorganizzazione su 6 aree")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function areeAction(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		$aree = [
			'NORD-OVEST'	=>1,
			'NORD-EST'		=>3,
			'CENTRO-NORD'	=>4,
			'CENTRO'		=>8,
			'SUD-OVEST'		=>7,
			'SUD-EST'		=>10
		];
		$pattern = array('/(A\'\s)/',	'/(E\'\s)/','/(I\'\s)/','/(O\'\s)/','/(U\'\s)/',	'/(A\')$/',	'/(E\')$/',	'/(I\')$/',	'/(O\')$/',	'/(U\')$/'	);
		$replace = array('à ',			'è ',		'ì ',		'ò ',		'ù ',			'à',		'è',		'ì',		'ò',		'ù'			);

		// districts
		$this->log('Aggiornamento DISTRICTS');
		$pdoDistricts = $this->pdoPrepare('INSERT INTO geo_districts (id, area_id, nome, status) VALUES (:id, :area_id, :nome, "ON") ON DUPLICATE KEY UPDATE area_id = :area_id2, nome = :nome2, status = "ON"');
		$processFn = function($i, $line, $values) use ($pdoDistricts, $aree, $pattern, $replace) {
			$areaID = $aree[$values[6]];
			$districtID = $values[7];
			$districtName = ucwords(strtolower(preg_replace($pattern, $replace, $values[8])));
			$pdoDistricts->execute([
				'id'=>$districtID,
				'area_id'=>$areaID, 'area_id2'=>$areaID,
				'nome'=>$districtName, 'nome2'=>$districtName
			]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE_AREE_DISTRICT_AGENZIE, 9, $processFn);
		$this->log(chr(9).sprintf(self::FILE_AREE_DISTRICT_AGENZIE.' OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
		}

		// agenzie
		$this->log('Aggiornamento AGENZIE');
		$pdoAgenzie = $this->pdoPrepare('UPDATE agenzie SET area = :area, district = :district WHERE id = :id');
		$processFn = function($i, $line, $values) use ($pdoAgenzie, $aree) {
			$agenziaID = (($values[0][0]=='0') ? 'G':'N') . substr($values[0],3);
			$areaID = $aree[$values[6]];
			$districtID = $values[7];
			$pdoAgenzie->execute([
				'id'=>$agenziaID,
				'area'=>$areaID,
				'district'=>$districtID
			]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE_AREE_DISTRICT_AGENZIE, 9, $processFn);
		$this->log(chr(9).sprintf(self::FILE_AREE_DISTRICT_AGENZIE.' OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'['.$error['errData']['code'].'] '.$error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
		}
	}
}
