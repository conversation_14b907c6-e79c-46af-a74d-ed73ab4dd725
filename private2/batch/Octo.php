<?php

namespace batch;


use data\apps\octo\Managers\PolicyManager;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response,
    metadigit\lib\util\csv\CsvProcessor;

use const metadigit\core\UPLOAD_DIR;

class Octo extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface
{
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @var PolicyManager
     */
    protected $policyManager;

    const DATE_COLUMNS = [
        'expirationReceipt', 'extractionDate', 'issueDate', 'collectionDate', 'effectDate'
    ];

    /**
     * @batch(description="Octo: OCTO_PORTAFOGLIO (quietanzamento mensile)")
     * @param Request $Req
     * @param Response $Res
     */
    public function policyMonthlyExpirationAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::POLICY_MONTHLY_EXPIRATION ==============: ');

        // Agency
        $agencies = $this->getAgencies();

        $files = glob(UPLOAD_DIR . "OCTO_PORTAFOGLIO*.csv");
        $query = "INSERT INTO octo_policy_expiration_monthly ( agency_id, area, district, subAgencyCode, producerCode, producer, policyNumber, licencePlate, productCode, sector,
                                                       receiptMonth, expirationReceipt, contractSeniorinity, clientSegmentFocus, boxType,
                                                       fee, rcaPrevYear, rcaFeePrevYear, rcaMinAutocontrollo, rcaMinGuidamica, clientSavings,
                                                       createdAt, updatedAt)
                  VALUES ( :agency_id, :area, :district, :subAgencyCode, :producerCode, :producer, :policyNumber, :licencePlate, :productCode, :sector, :receiptMonth, :expirationReceipt,
                           :contractSeniorinity, :clientSegmentFocus, :boxType, :fee, :rcaPrevYear, :rcaFeePrevYear, :rcaMinAutocontrollo, :rcaMinGuidamica,
                           :clientSavings, :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE agency_id = :agency_id, area = :area, district = :district, subAgencyCode = :subAgencyCode,
                           producerCode = :producerCode, producer = :producer, policyNumber = :policyNumber, licencePlate = :licencePlate, productCode = :productCode,
                           sector = :sector, receiptMonth = :receiptMonth, expirationReceipt = :expirationReceipt, contractSeniorinity = :contractSeniorinity,
                           clientSegmentFocus = :clientSegmentFocus, boxType = :boxType, fee = :fee, rcaPrevYear = :rcaPrevYear, rcaFeePrevYear = :rcaFeePrevYear,
                           rcaMinAutocontrollo = :rcaMinAutocontrollo, rcaMinGuidamica = :rcaMinGuidamica, clientSavings = :clientSavings, updatedAt = :updatedAt ";

        $fileColumns = $this->matchPolicyExpirationFields();

        foreach ($files as $filename) {

            if (! $fields = $this->getColumnFields($filename, $fileColumns)) {
                $this->log('POLICY_MONTHLY_EXPIRATION - Si è verificato un errore nel recupero delle colonne');
                return;
            }

            $this->importCsv($filename, $query, $fields, true, $agencies);
        }

        $this->log('== END::POLICY_MONTHLY_EXPIRATION =============== ');
    }

    /**
     * @batch(description="Octo: OCTO_ANNUALE_PORTAFOGLIO (quietanzamento annuale)")
     * @param Request $Req
     * @param Response $Res
     */
    public function policyAnnualExpirationAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::POLICY_ANNUAL_EXPIRATION ==============: ');

        // Agency
        $agencies = $this->getAgencies();

        if (! $this->truncateAnnualExpirationTable()) {
            $this->log('POLICY_MONTHLY_EXPIRATION - Si è verificato un errore nel troncamento della tabella');
            return;
        }

        $files = glob(UPLOAD_DIR . "OCTO_ANNUALE_PORTAFOGLIO*.csv");
        $query = "INSERT INTO octo_policy_expiration ( agency_id, area, district, subAgencyCode, producerCode, producer, policyNumber, licencePlate, productCode, sector,
                                                       receiptMonth, expirationReceipt, contractSeniorinity, clientSegmentFocus, boxType,
                                                       fee, rcaPrevYear, rcaFeePrevYear, rcaMinAutocontrollo, rcaMinGuidamica, clientSavings,
                                                       createdAt, updatedAt)
                  VALUES ( :agency_id, :area, :district, :subAgencyCode, :producerCode, :producer, :policyNumber, :licencePlate, :productCode, :sector, :receiptMonth, :expirationReceipt,
                           :contractSeniorinity, :clientSegmentFocus, :boxType, :fee, :rcaPrevYear, :rcaFeePrevYear, :rcaMinAutocontrollo, :rcaMinGuidamica,
                           :clientSavings, :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE agency_id = :agency_id, area = :area, district = :district, subAgencyCode = :subAgencyCode,
                           producerCode = :producerCode, producer = :producer, policyNumber = :policyNumber, licencePlate = :licencePlate, productCode = :productCode,
                           sector = :sector, receiptMonth = :receiptMonth, expirationReceipt = :expirationReceipt, contractSeniorinity = :contractSeniorinity,
                           clientSegmentFocus = :clientSegmentFocus, boxType = :boxType, fee = :fee, rcaPrevYear = :rcaPrevYear, rcaFeePrevYear = :rcaFeePrevYear,
                           rcaMinAutocontrollo = :rcaMinAutocontrollo, rcaMinGuidamica = :rcaMinGuidamica, clientSavings = :clientSavings, updatedAt = :updatedAt ";

        $fileColumns = $this->matchPolicyExpirationFields();

        foreach ($files as $filename) {

            if (! $fields = $this->getColumnFields($filename, $fileColumns)) {
                $this->log('POLICY_ANNUAL_EXPIRATION - Si è verificato un errore nel recupero delle colonne');
                return;
            }

            $this->importCsv($filename, $query, $fields, true, $agencies);
        }

        $this->log('== END::POLICY_ANNUAL_EXPIRATION =============== ');
    }

    /**
     * @batch(description="Octo: OCTO_MENSILE (statico mensile)")
     * @param Request $Req
     * @param Response $Res
     */
    public function objectStaticFileMonthlyAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::OBJ_STATIC_FILE_MONTHLY ==============: ');

        $files = glob(UPLOAD_DIR . "OCTO_MENSILE*.csv");
        $query = "INSERT INTO octo_obj_static_monthly_file ( initiative_id, agency_id, objInPezzi, createdAt, updatedAt )
                  VALUES ( :initiative_id, :agency_id, :objInPezzi, :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE initiative_id = :initiative_id, agency_id = :agency_id, objInPezzi = :objInPezzi, updatedAt = :updatedAt ";

        $fileColumns = $this->matchObjectStaticFileMonthlyFields();

        foreach ($files as $filename) {

            if (! $fields = $this->getColumnFields($filename, $fileColumns)) {
                $this->log('OBJ_STATIC_FILE_MONTHLY - Si è verificato un errore nel recupero delle colonne');
                return;
            }

            $this->importCsv($filename, $query, $fields);
        }

        $this->log('== END::OBJ_STATIC_FILE_MONTHLY =============== ');
    }

    /**
     * @batch(description="Octo: OCTO_STATICO (statico annuale)")
     * @param Request $Req
     * @param Response $Res
     */
    public function objectStaticFileAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::OBJ_STATIC_FILE ==============: ');

        $files = glob(UPLOAD_DIR . "OCTO_STATICO*.csv");
        $query = "INSERT INTO octo_obj_static_file ( agency_id, objInPezzi, createdAt, updatedAt )
                  VALUES ( :agency_id, :objInPezzi, :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE agency_id = :agency_id, objInPezzi = :objInPezzi, updatedAt = :updatedAt ";

        $fileColumns = $this->matchObjectStaticFileFields();

        foreach ($files as $filename) {

            if (! $fields = $this->getColumnFields($filename, $fileColumns)) {
                $this->log('OBJ_STATIC_FILE - Si è verificato un errore nel recupero delle colonne');
                return;
            }

            $this->importCsv($filename, $query, $fields);
        }

        $this->log('== END::OBJ_STATIC_FILE =============== ');
    }

    /**
     * @batch(description="Octo: OCTO_POLIZZE_CONVERTITE")
     * @param Request $Req
     * @param Response $Res
     */
    public function objectDynamicFileAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::OBJ_DYNAMIC_FILE =============== ');

//        if (! $this->truncateObjectDynamicFileTable()) {
//            $this->log('OBJ_DYNAMIC_FILE - Si è verificato un errore nel troncamento della tabella');
//            return;
//        }

        $files = glob(UPLOAD_DIR . "OCTO_POLIZZE_CONVERTITE*.csv");
        $query = "INSERT INTO octo_obj_dynamic_file ( initiative_id, raceType, extractionDate, issueDate, collectionDate, effectDate, campaign_id,
                                                     danniVita, agency_id, intermediary_id, delegation, branch, policyNumber, licencePlate, clientName, sector, clientCfIva,
                                                     productCode, motivation, productFamily, feeYearTaxable, feeUniqueTaxable, additionalPayments, feeDelta, feeEligible,
                                                     createdAt, updatedAt )
                  VALUES ( :initiative_id, :raceType, :extractionDate, :issueDate, :collectionDate, :effectDate, :campaign_id,
                           :danniVita, :agency_id, :intermediary_id, :delegation, :branch, :policyNumber, :licencePlate, :clientName, :sector, :clientCfIva,
                           :productCode, :motivation, :productFamily, :feeYearTaxable, :feeUniqueTaxable, :additionalPayments, :feeDelta, :feeEligible,
                           :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE initiative_id = :initiative_id, raceType = :raceType, extractionDate = :extractionDate, issueDate = :issueDate,
                           collectionDate = :collectionDate, effectDate = :effectDate, campaign_id = :campaign_id, danniVita = :danniVita,
                           agency_id = :agency_id, intermediary_id = :intermediary_id, delegation = :delegation, branch = :branch, policyNumber = :policyNumber,
                           licencePlate = :licencePlate, clientName = :clientName, sector = :sector, clientCfIva = :clientCfIva,
                           productCode = :productCode, motivation = :motivation, productFamily = :productFamily, feeYearTaxable = :feeYearTaxable,
                           feeUniqueTaxable = :feeUniqueTaxable, additionalPayments = :additionalPayments, feeDelta = :feeDelta, feeEligible = :feeEligible,
                           updatedAt = :updatedAt ";

        $fileColumns = $this->matchObjectDynamicFileFields();

        foreach ($files as $filename) {

            if (! $fields = $this->getColumnFields($filename, $fileColumns)) {
                $this->log('OBJ_DYNAMIC_FILE - Si è verificato un errore nel recupero delle colonne');
                return;
            }

            $this->importCsv($filename, $query, $fields, true, [], true);
        }

        $this->log('== END::OBJ_DYNAMIC_FILE =============== ');
    }

    /**
     * @batch(description="Octo: compute converted policies monthly")
     * @param Request $Req
     * @param Response $Res
     * @throws \Exception
     */
    public function saveConvertedMonthlyAction(Request $Req, Response $Res)
    {
        $this->log('== BEGIN::CONVERTED_MONTHLY =============== ');

        if (! $converted = $this->getNewConvertedMonthly()) {
            return;
        }

        $query = "INSERT INTO octo_policy_converted_monthly ( agency_id, producerCode, producer, licencePlate, oldPolicyNumber, newPolicyNumber, conversionType,
                                                      conversionDate, conversionPrice, oldPrice, oldFeePrice, bonus, createdAt, updatedAt )
                  VALUES ( :agency_id, :producerCode, :producer, :licencePlate, :oldPolicyNumber, :newPolicyNumber, :conversionType, :conversionDate, :conversionPrice,
                           :oldPrice, :oldFeePrice, :bonus, :createdAt, :updatedAt )
                  ON DUPLICATE KEY UPDATE agency_id = :agency_id, producerCode = :producerCode, producer = :producer, licencePlate = :licencePlate, oldPolicyNumber = :oldPolicyNumber,
                           newPolicyNumber = :newPolicyNumber, conversionType = :conversionType, conversionDate = :conversionDate, conversionPrice = :conversionPrice,
                           oldPrice = :oldPrice, oldFeePrice = :oldFeePrice, bonus = :bonus, updatedAt = :updatedAt ";

        $stmt  = $this->pdoPrepare($query);

        foreach ($converted as $convPolicy) {
            $bonus = $this->calculateBonus($convPolicy['conversionType'], $convPolicy['W'], $convPolicy['U'], $convPolicy['V'], $convPolicy['S']);

            $policy = [
                'agency_id' => $convPolicy['agency_id'],
                'producerCode' => $convPolicy['producerCode'],
                'producer' => $convPolicy['producer'],
                'licencePlate' => $convPolicy['licencePlate'],
                'oldPolicyNumber' => $convPolicy['oldPolicyNumber'],
                'newPolicyNumber' => $convPolicy['newPolicyNumber'],
                'conversionType' => $this->determineConversionType($convPolicy['conversionType'], $bonus),
                'conversionDate' => $convPolicy['conversionDate'],
                'conversionPrice' => $convPolicy['conversionPrice'],
                'oldPrice' => $convPolicy['oldPrice'],
                'oldFeePrice' => $convPolicy['oldFeePrice'],
                'bonus' => $bonus,
                'createdAt' => (new \DateTime())->format('Y-m-d H:i:s'),
                'updatedAt' => (new \DateTime())->format('Y-m-d H:i:s')
            ];

            $stmt->execute($policy);
        }

        $this->log('== END::CONVERTED_MONTHLY =============== ');
    }

    /**
     * @batch(description="Octo: build cache")
     * @param Request $Req
     * @param Response $Res
     * @throws \Exception
     */
    public function cacheAction(Request $Req, Response $Res)
    {
        $this->pdoExec("truncate octo_cache_dashboard");

        $statementExpired = $this->pdoQuery("select id, expirationReceipt from octo_policy_expiration ");
        $statementConverted = $this->pdoQuery("select * from vw_octo_obj_dynamic_file ");

        $expired = $statementExpired->fetchAll();
        $converted = $statementConverted->fetchAll();

        $this->makeCache($expired, $converted);
    }

    /**
     * Create the cache data
     *
     * @param $policiesExpiration
     * @param $policiesConverted
     * @throws \Exception
     */
    protected function makeCache($policiesExpiration, $policiesConverted)
    {
        $convertedInPeriod = $this->policyManager->convertedInPeriod($policiesConverted);
        $donut             = json_encode($this->policyManager->doughnutConverted($policiesConverted, count($policiesExpiration)));
        $bars              = json_encode($this->policyManager->convertedByMonths($policiesConverted, $policiesExpiration));
        $table             = json_encode($convertedInPeriod['table']);
        $converted         = json_encode($this->policyManager->converted(count($policiesConverted), count($policiesExpiration)));

        $row = [
            "doughnutConverted" => base64_encode($donut),
            "convertedByMonth" => base64_encode($bars),
            "yearlyConversionTable" => base64_encode($table),
            "yearlyConversionAccrued" => $convertedInPeriod['accrued'],
            "converted" => base64_encode($converted),
            "lastUpdate" => (new \DateTime('now'))->format('Y-m-d H:i:s')
        ];

        $this
            ->pdoExec("insert into octo_cache_dashboard (
            doughnutConverted,
            convertedByMonth,
            yearlyConversionTable,
            yearlyConversionAccrued,
            converted,
            lastUpdate
            )
            values (
                '{$row['doughnutConverted']}',
                '{$row['convertedByMonth']}',
                '{$row['yearlyConversionTable']}',
                '{$row['yearlyConversionAccrued']}',
                '{$row['converted']}',
                '{$row['lastUpdate']}'
            )");
    }

    private function isExist($filename)
    {
        if (! file_exists($filename)) {
            return false;
        }

        return true;
    }

    private function importCsv($filename, $query, $fields, $datetimes = 'true', $agencies = [], $dynamic = false)
    {
        if (! $this->isExist($filename)) {
            $this->log("$filename NON PRESENTE");
            return;
        }

        $csvProcessor = new CsvProcessor(['csvDelimiter' => ';', 'csvEnclosure' => '']);

        $pdoSt     = $this->pdoPrepare($query);
        $processFn = function ($i, $line, $values) use ($pdoSt, $fields, $datetimes, $agencies, $dynamic) {
            // Remove first row: header
            if ($i == 1) {
                return [true, null];
            }

            $associations = [];

            foreach ($fields as $key => $position) {
                $associations[$key] = $this->double(trim($values[$position]));

                if (in_array($key,self::DATE_COLUMNS)) {
                    $associations[$key] = \DateTime::createFromFormat("d/m/Y", $associations[$key])->format('Y-m-d');
                }
            }

            if ($dynamic) {
                $associations['agency_id'] = $this->makeAgencyId($associations['campaign_id'], $associations['agency_id']);
            }

            if (count($agencies) > 0) {
                $agencyId = trim($associations['agency_id']);

                if (isset($agencies[$agencyId])) {
                    $associations['area']     = $agencies[$agencyId]['area'];
                    $associations['district'] = $agencies[$agencyId]['district'];
                } else {
                    $associations['area']     = null;
                    $associations['district'] = null;
                }
            }

            if ($datetimes) {
                $associations['createdAt'] = (new \DateTime())->format('Y-m-d H:i:s');
                $associations['updatedAt'] = (new \DateTime())->format('Y-m-d H:i:s');
            }

            $pdoSt->execute($associations);

            return [true, null];
        };

        list($success, $ok, $err, $skip, $tot, $stats, $errors) = $csvProcessor->processFile($filename, count($fields), $processFn);

        $this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));

        foreach ($errors as $error) {
            $msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
                'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
                : $error['errMsg'];

            $this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
        }
    }

    /**
     * Detect position of columns
     *
     * @param $filename
     * @param $fileColumns
     * @return array|bool
     */
    private function getColumnFields($filename, $fileColumns)
    {
        $columns = 0;
        $row     = 0;
        $fields  = [];
        $handle  = fopen($filename, "r");

        while ($data = fgetcsv($handle,1000,";")) {
            if ($row > 0) {
                break;
            }

            $columns = count($data);    // Count columns

            for ($c = 0; $c < $columns; $c++) {
                if (empty($key = $fileColumns[$this->clean($data[$c])])) {
                    $this->log("La colonna {$this->clean($data[$c])} non ha corrispondenza nel database, controlla il file.");
                    return false;
                }

                $fields[$key] = $c;
            }

            $row++;
        }

        if (count($fields) == 0) {
            $this->log("Il file $filename non ha colonne. Verifica che non sia vuoto.");
            return false;
        }

        return $fields;
    }

    private function clean($string)
    {
        $string = iconv("Windows-1252", "UTF-8", $string);

        return str_replace("°", "", $string);
    }

    private function double($string)
    {
        $stringWithDot = str_replace(",", ".", $string);

        if (is_numeric($stringWithDot)) {
            return $stringWithDot;
        }

        return $string;
    }

    /**
     * Match of the file columns with the database table columns
     * for policyExpiration table
     *
     * @return array
     */
    private function matchPolicyExpirationFields()
    {
        return [
            'COD AGENZIA' => 'agency_id',
            'COD SUBAGE' => 'subAgencyCode',
            'COD PRODUTTORE' => 'producerCode',
            'PRODUTTORE' => 'producer',
            'N POLIZZA' => 'policyNumber',
            'TARGA' => 'licencePlate',
            'COD PRODOTTO' => 'productCode',
            'SETTORE' => 'sector',
            'MESE ESTRAZIONE' => 'receiptMonth',
            'SCAD. QUIETANZA' => 'expirationReceipt',
            'ANZIANITA CONTRATTO' => 'contractSeniorinity',
            'FOCUS CLIENTE' => 'clientSegmentFocus',
            'TIPO BOX' => 'boxType',
            'IMP. CANONE' => 'fee',
            'PREMIO NETTO RCA ANNO PREC' => 'rcaPrevYear',
            'NETTO RCA ANNO PREC + CANONE' => 'rcaFeePrevYear',
            'NET RCA ANN CORR ACTRL MIN INC' => 'rcaMinAutocontrollo',
            'NET RCA ANN CORR GAMIC MIN INC' => 'rcaMinGuidamica',
            'RISP CLIENT X PASSAG A G.AMICA' => 'clientSavings'
        ];
    }

    /**
     * Match of the file columns with the database table columns
     * for objStaticFileMonthly table
     *
     * @return array
     */
    private function matchObjectStaticFileMonthlyFields()
    {
        return [
            'Id iniziativa' => 'initiative_id',
            'Id agenzia' => 'agency_id',
            'objinzpezzi' => 'objInPezzi'
        ];
    }

    /**
     * Match of the file columns with the database table columns
     * for objStaticFile table
     *
     * @return array
     */
    private function matchObjectStaticFileFields()
    {
        return [
            'Id agenzia' => 'agency_id',
            'objinzpezzi' => 'objInPezzi'
        ];
    }

    /**
     * Match of the file columns with the database table columns
     * for objDynamicFile table
     *
     * @return array
     */
    private function matchObjectDynamicFileFields()
    {
        return [
            'ID INIZIATIVA' => 'initiative_id',
            'TIPO DI GARA' => 'raceType',
            'DATA ESTRAZIONE' => 'extractionDate',
            'DATA EMISSIONE' => 'issueDate',
            'DATA INCASSO' => 'collectionDate',
            'DATA EFFETTO' => 'effectDate',
            'ID COMPAGNIA' => 'campaign_id',
            'DANNI/VITA' => 'danniVita',
            'ID AGENZIA' => 'agency_id',
            'ID INTERMEDIARIO' => 'intermediary_id',
            'DELEGA' => 'delegation',
            'RAMO' => 'branch',
            'NUMERO POLIZZA' => 'policyNumber',
            'TARGA' => 'licencePlate',
            'NOMINATIVO CLIENTE' => 'clientName',
            'SETTORE' => 'sector',
            'CODICE FISC/P.IVA' => 'clientCfIva',
            'CODICE PRODOTTO' => 'productCode',
            'MOTIVO ENTRATA' => 'motivation',
            'FAMIGLIA PRODOTTO' => 'productFamily',
            'PREMIO ANNUO IMPONIBILE' => 'feeYearTaxable',
            'PREMIO UNICO IMPONIBILE' => 'feeUniqueTaxable',
            'VERSAMENTI AGGIUNTIVI' => 'additionalPayments',
            'DELTA PREMIO' => 'feeDelta',
            'PREMIO COMPUTABILE' => 'feeEligible'
        ];
    }

    /**
     * Retrieve the policy converted by new data inserted
     *
     * @return array
     */
    private function getNewConvertedMonthly()
    {
        $query = "SELECT e.agency_id, e.producerCode, e.producer, e.licencePlate, e.policyNumber AS oldPolicyNumber, d.policyNumber AS newPolicyNumber,
                         d.productCode AS conversionType, e.expirationReceipt AS conversionDate, d.additionalPayments AS conversionPrice, e.rcaPrevYear AS oldPrice,
                         e.rcaFeePrevYear AS oldFeePrice, d.additionalPayments AS W, d.feeUniqueTaxable AS V, d.feeYearTaxable AS U, d.motivation AS S
                    FROM octo_policy_expiration_monthly AS e
                         LEFT JOIN octo_obj_dynamic_file AS d ON e.licencePlate = d.licencePlate
                         LEFT JOIN octo_policy_converted_monthly AS c ON e.licencePlate = c.licencePlate
                   WHERE d.id IS NOT NULL
                     AND c.id IS NULL";

        return $this->pdoQuery($query)->fetchAll();
    }

    /**
     * Truncate the octo_obj_dynamic_file table
     *
     * @return bool
     */
    private function truncateObjectDynamicFileTable()
    {
        $query = "TRUNCATE octo_obj_dynamic_file";

        if (! $this->pdoPrepare($query)->execute()) {
            return false;
        }

        return true;
    }

    /**
     * Truncate the octo_policy_expiration table
     *
     * @return bool
     */
    private function truncateAnnualExpirationTable()
    {
        $query = "TRUNCATE octo_policy_expiration";

        if (! $this->pdoPrepare($query)->execute()) {
            return false;
        }

        return true;
    }

    /**
     * Get agencies
     *
     * @return array
     */
    private function getAgencies()
    {
        $agenciesArr = [];

        $query = "SELECT id, area, district FROM agenzie WHERE status = 'ON'";

        $agencies = $this->pdoQuery($query)->fetchAll();

        foreach ($agencies as $row) {
            $agenciesArr[$row['id']] = [
                'area' => $row['area'],
                'district' => $row['district']
            ];
        }

        return $agenciesArr;
    }

    /**
     * Calculate bonus
     *
     * @param $conversionType
     * @param $w, column w
     * @param $u, column u
     * @param $v, column v
     * @param $motivation, column s
     * @return int
     */
    private function calculateBonus($conversionType, $w, $u, $v, $motivation)
    {
        // ---> CASO motivation = S <---

        if (trim($motivation) == "S") {
            if (trim($conversionType) == "A") {
                return 3;
            }

            if (trim($conversionType) == "G") {
                return 6;
            }

            return 0;
        }

        // ---> ALTRIMENTI <---

        if (trim($conversionType) == "A") {
            $x = $w - $u;   // X = NEW - OLD

            if ($x >= 0) {
                return 3;
            }

            if (abs($x) < 1) {
                return 3;
            }
        }

        if (trim($conversionType) == "G") {
            $f = 0.95 * $v; // F = 95% V
            $x = $w - $f;   // X = NEW - F

            if ($x >= 0) {
                return 6;
            }

            if (abs($x) < 1) {
                return 6;
            }
        }

        return 0;
    }

    /**
     * Determine conversion type
     *
     * @param $conversionType
     * @param $bonus
     * @return string
     */
    private function determineConversionType($conversionType, $bonus)
    {
        if ($conversionType == "A") {
            if ($bonus > 0) {
                return "AUTOCONTROLLO_BONUS";
            }

            return "AUTOCONTROLLO";
        }

        if ($conversionType == "G") {
            if ($bonus > 0) {
                return "GUIDAMICA_BONUS";
            }

            return "GUIDAMICA";
        }

        return "NOT_CONVERTED";
    }

    /**
     * Create agencyId
     *
     * @param $campaignIdAssoc
     * @param $agencyIdAssoc
     * @return string
     * @throws \Exception
     */
    private function makeAgencyId($campaignIdAssoc, $agencyIdAssoc)
    {
        $agencyIdAssoc = trim($agencyIdAssoc);
        $length        = strlen($agencyIdAssoc);

        if (($length < 1) || ($length > 3)) {
            throw new \Exception("The agencyId $agencyIdAssoc is not valid");
        }

        switch ($length) {
            case 1:
                $agencyIdAssoc = "00".$agencyIdAssoc;
                break;
            case 2:
                $agencyIdAssoc = "0".$agencyIdAssoc;
                break;
        }

        return trim($campaignIdAssoc).$agencyIdAssoc;
    }
}
