<?php

namespace data\apps\accordo2;


use data\apps\accordo2\Repositories\AccordoRepository;
use data\apps\accordo2\Repositories\AccordoRevRepository;
use data\apps\accordo2\Repositories\FasciaRamiPrefRepository;
use data\apps\accordo2\Repositories\FasciaVitaRepository;
use data\apps\accordo2\Repositories\RappelRamiPrefRepository;
use data\apps\accordo2\Repositories\RappelVitaRepository;
use metadigit\core\db\orm\OrmEvent;

class ACL
{
    use \metadigit\core\CoreTrait;

    function onFetch(OrmEvent $Event)
    {
        $criteriaExp = null;

        if ($repository = $Event->getRepository() instanceof AccordoRepository ||
            $repository = $Event->getRepository() instanceof AccordoRevRepository) {
            switch($_SESSION['AUTH']['UTYPE']) {
                case 'KA':
                case 'PIAN':
                case 'AMMINISTRATORE':
                    break;
                case 'AREAMGR':
                case 'ASV':
                    $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
                    break;
                case 'DISTRICTMGR':
                    $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'].'|district,EQ,'.$_SESSION['AUTH']['DISTRICT'];
                    break;
                default:
                    throw new \Exception('0 ACCESS DENIED');
            }
        }

        if ($repository = $Event->getRepository() instanceof FasciaVitaRepository ||
            $repository = $Event->getRepository() instanceof FasciaRamiPrefRepository ||
            $repository = $Event->getRepository() instanceof RappelVitaRepository ||
            $repository = $Event->getRepository() instanceof RappelRamiPrefRepository) {
            switch($_SESSION['AUTH']['UTYPE']) {
                case 'KA':
                case 'PIAN':
                case 'AMMINISTRATORE':
                case 'AREAMGR':
                case 'DISTRICTMGR':
                    break;
                default:
                    throw new \Exception('0 ACCESS DENIED');
            }
        }

        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'].': '.$criteriaExp);

        $Event->criteriaExp($criteriaExp);
    }
}
