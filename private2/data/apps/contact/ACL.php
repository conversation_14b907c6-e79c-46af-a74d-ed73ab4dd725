<?php
namespace data\apps\contact;
use metadigit\core\db\orm\OrmEvent;

class ACL {
	use \metadigit\core\CoreTrait;

	function onFetch(OrmEvent $Event) {
		$criteriaExp = null;
		switch($_SESSION['AUTH']['UTYPE']) {
			case 'KA':
			case 'AMMINISTRATORE':
				break;
			case 'AREAMGR':
			case 'FOR':
				$criteriaExp = 'area,'.$_SESSION['AUTH']['AREA'];
				break;
			case 'ASV':
				if($_SESSION['AUTH']['AREA']) $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
				break;
			case 'DISTRICTMGR':
				$criteriaExp = 'district,'.$_SESSION['AUTH']['DISTRICT'];
				break;
			case 'AGENTE':
				$criteriaExp = 'agenzia,'.$_SESSION['AUTH']['AGENZIA'];
				break;
			default:
				throw new \Exception('ACCESS DENIED');
		}
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'].': '.$criteriaExp);
		$Event->criteriaExp($criteriaExp);
	}
}
