<?php
namespace data\apps\contact;
/**
 * @orm(source="app_contact_lead")
 * @orm-criteria(area="agenzia_id IN (SELECT id FROM agenzie WHERE area = ?1)")
 * @orm-criteria(district="agenzia_id IN (SELECT id FROM agenzie WHERE district = ?1)")
 * @orm-criteria(agenzia="agenzia_id = ?1")
 * @orm-criteria(active="( status IS NULL OR status != 'ELIMINATO' )")
 */
class ClienteLead {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm */
	protected $agenzia_id;
	/** @orm(type="date", null) */
	protected $data;

	// anagrafica

	/** @orm */
	protected $nome;
	/** @orm */
	protected $cognome;
	/** @orm */
	protected $email;
	/** @orm */
	protected $telefono;
	/** @orm */
	protected $indirizzo;
	/** @orm(type="string") */
	protected $cap;
	/** @orm */
	protected $localita;
	/** @orm(type="integer") */
	protected $eta;

	// dati assicurativi

	/** @orm */
	protected $dataScadenza;
	/** @orm(primarykey) */
	protected $targa;
	/** @orm */
	protected $marca;
	/** @orm */
	protected $modello;
	/** @orm(type="integer") */
	protected $cavalli;

	// status

	/** @orm(null)
	 * @validate(null, regex="/^(CONTATTATO|OK|KO|ELIMINATO)$/") */
	protected $status;

	// system data

	/** @orm(type="datetime", null) */
	protected $updatedAt;
}
