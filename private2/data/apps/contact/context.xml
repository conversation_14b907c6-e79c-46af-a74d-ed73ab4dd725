<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.apps.contact">
	<objects>

		<!-- === REPOSITORIES ================================================================= -->

		<object id="data.apps.contact.ClientiLeadRepository" class="metadigit\core\db\orm\Repository">
			<constructor>
				<arg name="class">data\apps\contact\ClienteLead</arg>
			</constructor>
		</object>
		<object id="data.apps.contact.ClientiUscitiRepository" class="metadigit\core\db\orm\Repository">
			<constructor>
				<arg name="class">data\apps\contact\ClienteUscito</arg>
			</constructor>
		</object>

		<!-- === MANAGERS ================================================================= -->

		<object id="data.apps.contact.ACL" class="data\apps\contact\ACL"/>
	</objects>
	<events>
		<event name="orm:pre-count">
			<listeners>
				<listener>data.apps.contact.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:pre-fetch-all">
			<listeners>
				<listener>data.apps.contact.ACL->onFetch</listener>
			</listeners>
		</event>
	</events>
</context>
