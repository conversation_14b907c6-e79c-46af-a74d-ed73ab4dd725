<?php

namespace data\apps\eventmanager;

use metadigit\core\db\orm\OrmEvent;

class ACL
{
    use \metadigit\core\CoreTrait;

    function onFetch(OrmEvent $Event)
    {
        $criteriaExp = null;

        if ($repository = $Event->getRepository() instanceof UserRepository) {
            switch($_SESSION['AUTH']['UTYPE']) {
                case 'KA':
                case 'AMMINISTRATORE':
                case 'FORMAZIONE':
                    break;
                case 'AREAMGR':
                    $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
                    break;
                case 'DISTRICTMGR':
                    $criteriaExp = 'district,EQ,'.$_SESSION['AUTH']['DISTRICT'];
                    break;
                default:
                    throw new \Exception('0 ACCESS DENIED');
            }
        }

        if ($repository = $Event->getRepository() instanceof EnrollmentRepository) {
            switch($_SESSION['AUTH']['UTYPE']) {
                case 'KA':
                case 'AMMINISTRATORE':
                case 'FORMAZIONE':
                    break;
                case 'AREAMGR':
                    $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
                    break;
                case 'DISTRICTMGR':
                    $criteriaExp = 'district,EQ,'.$_SESSION['AUTH']['DISTRICT'];
                    break;
                default:
                    throw new \Exception('0 ACCESS DENIED');
            }
        }

        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'].': '.$criteriaExp);

        $Event->criteriaExp($criteriaExp);
    }
}
