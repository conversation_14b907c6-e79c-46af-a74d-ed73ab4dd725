<?php

namespace data\apps\formazione\Utils;


class Utilities
{
    public function findLimitSemester()
    {
        $currentYear  = date('Y');
        $currentMonth = date('n');

        if ($currentMonth >= 7) {
            $currentSemesterStart = date($currentYear . '-07-01 00:00:00');
            $currentSemesterEnd   = date($currentYear . '-12-31 23:59:59');
        } else {
            $currentSemesterStart = date($currentYear . '-01-01 00:00:00');
            $currentSemesterEnd   = date($currentYear . '-06-30 23:59:59');
        }

        return [
            "start" => $currentSemesterStart, "end" => $currentSemesterEnd
        ];
    }

    public function convertMinutesToHundredths($minutes)
    {
        $hundredths = 0;
        switch ($minutes) {
            case 0:
                $hundredths = 0;
                break;
            case 15:
                $hundredths = 25;
                break;
            case 30:
                $hundredths = 50;
                break;
            case 45:
                $hundredths = 75;
                break;
        }
        return $hundredths;
    }
}
