<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.apps.ivass">
	<objects>
		<object id="data.apps.ivass.IvassRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\Ivass</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table1Repository" class="data\apps\ivass\Repository\Table1Repository">
			<constructor><arg name="class">data\apps\ivass\Table1</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table2Repository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\Table2</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table3Repository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\Table3</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table5Repository" class="data\apps\ivass\Repository\Table5Repository">
			<constructor><arg name="class">data\apps\ivass\Table5</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table7Repository" class="data\apps\ivass\Repository\Table7Repository">
			<constructor><arg name="class">data\apps\ivass\Table7</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table8Repository" class="data\apps\ivass\Repository\Table8Repository">
			<constructor><arg name="class">data\apps\ivass\Table8</arg></constructor>
		</object>
		<object id="data.apps.ivass.Table8AuditsRepository" class="data\apps\ivass\Repository\Table8AuditsRepository">
			<constructor><arg name="class">data\apps\ivass\Table8Audit</arg></constructor>
		</object>
		<object id="data.apps.ivass.BancheRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\Table57Banche</arg></constructor>
		</object>
		<object id="data.apps.ivass.BancheView57Repository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\ViewTable57Banche</arg></constructor>
		</object>
		<object id="data.apps.ivass.Banche23Repository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\Table23Banche</arg></constructor>
		</object>
		<object id="data.apps.ivass.BancheView23Repository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\apps\ivass\ViewTable23Banche</arg></constructor>
		</object>
	</objects>
</context>
