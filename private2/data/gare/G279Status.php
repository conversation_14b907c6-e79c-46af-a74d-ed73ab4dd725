<?php
namespace data\gare;
/**
 * @orm(source="vw_gare_279", target="gare_279")
 * @orm-fetch-subset(classifica="agenzia_id, localita, pezziRisparmio, pezziProtezione, premi, posizione")
 */
class G279Status {
	use \metadigit\core\db\orm\EntityTrait;

	const AGGREGATE_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziRisparmio) AS pezziRisparmio,
		SUM(pezziProtezione) AS pezziProtezione,
		SUM(premi) AS premi,
		SUM(obiettivoPezziRisparmio) AS obiettivoPezziRisparmio,
		SUM(obiettivoPezziProtezione) AS obiettivoPezziProtezione,
		SUM(obiettivoPremi) AS obiettivoPremi
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm */
	protected $gruppo;

	/** @orm(type="integer") */
	protected $obiettivoPezziRisparmio;
	/** @orm(type="integer") */
	protected $obiettivoPezziProtezione;
	/** @orm(type="float") */
	protected $obiettivoPremi;
	/** @orm(type="integer") */
	protected $pezziRisparmio;
	/** @orm(type="integer") */
	protected $pezziProtezione;
	/** @orm(type="float") */
	protected $premi;
	/** @orm(type="integer") */
	protected $posizione;
}
