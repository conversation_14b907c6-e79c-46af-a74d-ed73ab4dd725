<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_117_quietanze")
 * @orm-criteria(area="")
 * @orm-criteria(district="")
 * @orm-criteria(agenzia="")
 */
class I117Quietanza {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(primarykey) */
	protected $polizza;
	/** @orm */
	protected $tariffa;
	/** @orm */
	protected $nome;

	/** @orm(type="date") */
	protected $dataDecorrenza;
	/** @orm(type="date") */
	protected $dataScadenza;

	/** @orm(type="float") */
	protected $importo;
	/** @orm(type="boolean") */
	protected $incasso;
}
