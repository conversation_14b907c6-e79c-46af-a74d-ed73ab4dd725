<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentivazioni">
	<objects>
		<object id="data.incentivazioni.ACL" class="data\incentivazioni\ACL"/>
		<object id="data.incentivazioni.IncentivAgzCacheRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\IncentivAgzCache</arg></constructor>
		</object>
		<object id="data.incentivazioni.IncentivazioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\Incentivazione</arg></constructor>
		</object>
		<object id="data.incentivazioni.PolizzeRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\Polizza</arg></constructor>
		</object>

		<!-- INIZIATIVE STANDARD -->

		<object id="data.incentivazioni.I52StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I52Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I53StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I53Status</arg></constructor>
		</object>
			<object id="data.incentivazioni.I58StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I58Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I59StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I59Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I60StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I60Status</arg></constructor>
		</object>
			<object id="data.incentivazioni.I61StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I61Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I62StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I62Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I63StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I63Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I64StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I64Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I69StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I69Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I70StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I70Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I70ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I70Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I71StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I71Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I72StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I72Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I73StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I73Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I74StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I74Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I75StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I75Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I76StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I76Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I77StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I77Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I83StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I83Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I84StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I84Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I85StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I85Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I86StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I86Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I86ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I86Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I87StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I87Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I88StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I88Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I89StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I89Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I90StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I90Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I92StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I92Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I93StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I93Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I94StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I94Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I95StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I95Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I95IntermediariRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I95Intermediario</arg></constructor>
		</object>
		<object id="data.incentivazioni.I95ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I95Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I95IscrizioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I95Iscrizione</arg></constructor>
		</object>
		<object id="data.incentivazioni.I96StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I96Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I96IntermediariRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I96Intermediario</arg></constructor>
		</object>
		<object id="data.incentivazioni.I96IscrizioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I96Iscrizione</arg></constructor>
		</object>
		<object id="data.incentivazioni.I97StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I97Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I102StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I102Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I103StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I103Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I104StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I104Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I104ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I104Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I105StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I105Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I105IntermediariRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I105Intermediario</arg></constructor>
		</object>
		<object id="data.incentivazioni.I105ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I105Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I105IscrizioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I105Iscrizione</arg></constructor>
		</object>
		<object id="data.incentivazioni.I107StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I107Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I108StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I108Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I110IntermediariRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I110Intermediario</arg></constructor>
		</object>
		<object id="data.incentivazioni.I110IscrizioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I110Iscrizione</arg></constructor>
		</object>
		<object id="data.incentivazioni.I113StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I113Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I114AreaStatusRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I114AreaStatus</arg></constructor>
		</object>
		<object id="data.incentivazioni.I114DistrictStatusRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I114DistrictStatus</arg></constructor>
		</object>
		<object id="data.incentivazioni.I114StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I114Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I115StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I115Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I117StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I117Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I117QuietanzeRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I117Quietanza</arg></constructor>
		</object>
		<object id="data.incentivazioni.I118StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I118Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I119StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I119Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I124StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I124Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I125StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I125Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I125IntermediariRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I125Intermediario</arg></constructor>
		</object>
		<object id="data.incentivazioni.I125ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I125Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I125IscrizioniRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I125Iscrizione</arg></constructor>
		</object>
		<object id="data.incentivazioni.I126StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I126Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I127StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I127Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I133StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I133Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I135StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I135Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I138StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I138Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I141StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I141Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I142StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I142Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I146StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I146Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I146MyAngelRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I146MyAngel</arg></constructor>
		</object>
		<object id="data.incentivazioni.I147StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I147Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I147ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I147Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I147MyAngelRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I147MyAngel</arg></constructor>
		</object>
		<object id="data.incentivazioni.I148StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I148Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I149StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I149Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I151StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I151Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I152StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I152Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I153StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I153Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I154StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I154Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I155StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I155Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I156StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I156Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I159StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I159Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I163StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I163Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I164StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I164Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I167StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I167Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I170StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I170Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I173StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I173Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I174StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I174Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I175StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I175Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I176StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I176Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I176ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I176Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I176MyAngelRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I176MyAngel</arg></constructor>
		</object>
		<object id="data.incentivazioni.I177StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I177Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I179StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I179Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I179ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I179Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I181StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I181Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I182StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I182Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I183StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I183Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I186StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I186Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I187StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I187Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I188StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I188Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I189StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I189Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I190StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I190Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I191StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I191Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I196StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I196Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I196ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I196Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I196MyAngelRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I196MyAngel</arg></constructor>
		</object>
		<object id="data.incentivazioni.I197StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I197Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I200StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I200Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I203StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I203Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I203ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I203Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I204StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I204Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I206StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I206Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I207StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I207Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I208StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I208Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I209StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I209Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I210StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I210Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I212StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I212Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I213StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I213Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I214StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I214Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I215StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I215Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I219StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I219Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I220StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I220Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I220ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I220Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I220MyAngelRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I220MyAngel</arg></constructor>
		</object>
		<object id="data.incentivazioni.I221StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I221Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I221ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I221Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I223StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I223Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I225StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I225Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I226StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I226Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I227StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I227Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I232StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I232Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I234StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I234Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I235StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I235Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I236StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I236Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I237StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I237Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I237ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I237Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I254StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I254Status</arg></constructor>
			<properties><property name="objGaType">PEZZI</property></properties>
		</object>
		<object id="data.incentivazioni.I254ClientiRepository" class="metadigit\core\db\orm\Repository">
			<constructor><arg name="class">data\incentivazioni\I254Cliente</arg></constructor>
		</object>
		<object id="data.incentivazioni.I256StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I256Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I257StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I257Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I258StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I258Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I259StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I259Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I260StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I260Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I261StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I261Status</arg></constructor>
		</object>
		<object id="data.incentivazioni.I268StatusRepository" class="data\incentivazioni\StatusRepository">
			<constructor><arg name="class">data\incentivazioni\I268Status</arg></constructor>
		</object>
	</objects>
	<events>
		<event name="orm:pre-count">
			<listeners>
				<listener>data.incentivazioni.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:pre-fetch">
			<listeners>
				<listener>data.incentivazioni.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:pre-fetch-all">
			<listeners>
				<listener>data.incentivazioni.ACL->onFetch</listener>
			</listeners>
		</event>
	</events>
</context>
