<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.infortuni">

    <objects>
        <object id="data.incentive.infortuni.StaticRepository" class="data\incentive\infortuni\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\infortuni\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.infortuni.StatusRepository" class="data\incentive\infortuni\StatusRepository">
            <constructor>
                <arg name="class">data\incentive\infortuni\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.infortuni.DataRepository" class="data\incentive\infortuni\DataRepository">
            <constructor>
                <arg name="class">data\incentive\infortuni\Data</arg>
            </constructor>
        </object>
        <object id="data.incentive.infortuni.MonitoringRepository" class="data\incentive\infortuni\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\infortuni\Monitoring</arg>
            </constructor>
        </object>
        <object id="data.incentive.infortuni.ACL" class="data\incentive\infortuni\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.infortuni.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.infortuni.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.infortuni.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
