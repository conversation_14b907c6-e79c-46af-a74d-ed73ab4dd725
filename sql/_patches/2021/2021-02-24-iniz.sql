ALTER TABLE app_reportistica_batches
	DROP FOREIGN KEY fk_app_reportistica_logs_id_iniz,
	DROP INDEX fk_app_reportistica_logs_id_iniz
;
ALTER TABLE app_reportistica_dati_iniziative
	DROP FOREIGN KEY app_reportistica_iniziative
;
ALTER TABLE iniz_cache
    DROP FOREIGN KEY fk_iniz_cache_id
;
ALTER TABLE iniz_polizze
    DROP FOREIGN KEY fk_polizze_iniz
;


ALTER TABLE iniz
	CHANGE id	id				SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT
;


ALTER TABLE app_reportistica_dati_iniziative
	CHANGE id	id				SMALLINT UNSIGNED NOT NULL,
	ADD CONSTRAINT fk_app_reportistica_dati_iniziative FOREIGN KEY (id) REFERENCES iniz (id)
;
ALTER TABLE app_reportistica_batches
    CHANGE id_iniz	id_iniz		SMALLINT UNSIGNED NULL DEFAULT NULL,
    ADD CONSTRAINT fk_app_reportistica_logs_id_iniz FOREIGN KEY (id_iniz) REFERENCES app_reportistica_dati_iniziative (id)
;
ALTER TABLE iniz_cache
	CHANGE id	id				SMALLINT UNSIGNED NOT NULL AUTO_INCREMENT,
	ADD CONSTRAINT fk_iniz_cache__id FOREIGN KEY (id) REFERENCES iniz (id)
;
ALTER TABLE iniz_polizze
	CHANGE iniziativa_id	iniziativa_id	SMALLINT UNSIGNED NOT NULL,
    ADD CONSTRAINT fk_iniz_polizze__iniz_id FOREIGN KEY (iniziativa_id) REFERENCES iniz (id)
;


DROP PROCEDURE IF EXISTS iniz_import_polizze_cvs;
DELIMITER //
CREATE PROCEDURE iniz_import_polizze_cvs (
	IN p_tipoGara			CHAR(2),
	IN p_iniziativa_id		SMALLINT UNSIGNED,
	IN p_dataEstrazione		DATE,
	IN p_dataEmissione		DATE,
	IN p_dataIncasso		DATE,
	IN p_dataEffetto		DATE,
	IN p_idCompagnia		CHAR(1),
	IN p_ptf				CHAR(1),
	IN p_idAgenzia			CHAR(3),
	IN p_codEsazione		VARCHAR(6),
	IN p_delega				CHAR(1),
	IN p_ramo				VARCHAR(2),
	IN p_polizza			VARCHAR(20),
	IN p_garanzia			VARCHAR(10),
	IN p_nomeCliente		VARCHAR(50),
	IN p_codiceCliente		VARCHAR(10),
	IN p_CF_PIVA			CHAR(16),
	IN p_codiceProdotto		VARCHAR(6),
	IN p_motivo				CHAR(1),
	IN p_famigliaProdotto	VARCHAR(10),
	IN p_premioAnnuo		DECIMAL(12,2) UNSIGNED,
	IN p_premioUnico		DECIMAL(12,2) UNSIGNED,
	IN p_versamAgg			DECIMAL(12,2) UNSIGNED,
	IN p_deltaPremio		DECIMAL(12,2),
	IN p_premioComputabile	DECIMAL(12,2),
	IN p_codiceCampagna		VARCHAR(10),
	IN p_codiceConvenzione	VARCHAR(10),
	IN p_codicePartnership	VARCHAR(10),
	IN p_tipoCollettiva		VARCHAR(2),
	IN p_numPolizzaMadre	VARCHAR(20),
	IN p_modulareVita		VARCHAR(2),
	IN p_OTP				VARCHAR(2),
	IN p_FEA				VARCHAR(2),
	IN p_TED				VARCHAR(2),
	IN p_PD					VARCHAR(2)
)
BEGIN
	DECLARE p_agenzia_id CHAR(4);
	DECLARE v_agenzia_id CHAR(4);
	SET p_agenzia_id = CONCAT(p_idCompagnia, p_idAgenzia);
	SET p_codEsazione = LPAD(TRIM(p_codEsazione), 6, 0);
	SET p_ramo = TRIM(p_ramo);
	SET p_polizza = LPAD(TRIM(p_polizza), 10, '0');
	SET p_garanzia = TRIM(p_garanzia);
	SET p_nomeCliente = TRIM(p_nomeCliente);
	SET p_codiceCliente = TRIM(p_codiceCliente);
	SET p_CF_PIVA = TRIM(p_CF_PIVA);
	SET p_codiceProdotto = TRIM(UPPER(p_codiceProdotto));
	SET p_motivo = TRIM(UPPER(p_motivo));
	SET p_famigliaProdotto = TRIM(UPPER(p_famigliaProdotto));

	SELECT id INTO v_agenzia_id FROM agenzie_alias
		WHERE alias = p_agenzia_id;
	IF v_agenzia_id IS NOT NULL THEN SET p_agenzia_id = v_agenzia_id; END IF;

	IF ( p_iniziativa_id = 7 OR p_iniziativa_id = 33 OR p_iniziativa_id = 49 OR p_iniziativa_id = 96 ) AND p_ptf = 'V' THEN
		INSERT INTO iniz_polizze (
			iniziativa_id, ptf,
			agenzia_id, codEsazione,
			dataEstrazione, dataEmissione, dataIncasso, dataEffetto,
			polizza, delega, ramo, garanzia,
			codiceProdotto, famigliaProdotto, motivo,
			nomeCliente, codiceCliente, CF_PIVA,
			premioAnnuo, premioUnico, versamAgg, deltaPremio, premioComputabile,
			codiceCampagna, codiceConvenzione, codicePartnership, tipoCollettiva, numPolizzaMadre, modulareVita
		) VALUES (
			p_iniziativa_id, p_ptf,
			p_agenzia_id, p_codEsazione,
			p_dataEstrazione, p_dataEmissione, p_dataIncasso, p_dataEffetto,
			p_polizza, p_delega, p_ramo, p_garanzia,
			p_codiceProdotto, p_famigliaProdotto, p_motivo,
			p_nomeCliente, p_codiceCliente, p_CF_PIVA,
			p_premioAnnuo, p_premioUnico, p_versamAgg, p_deltaPremio, p_premioComputabile,
			p_codiceCampagna, p_codiceConvenzione, p_codicePartnership, p_tipoCollettiva, p_numPolizzaMadre, p_modulareVita
		) ON DUPLICATE KEY UPDATE
			iniziativa_id = p_iniziativa_id, ptf = p_ptf,

			agenzia_id = p_agenzia_id, dataEstrazione = p_dataEstrazione, dataEmissione = p_dataEmissione, dataIncasso = p_dataIncasso, dataEffetto = p_dataEffetto,
			polizza = p_polizza, delega = p_delega, ramo = p_ramo, garanzia = p_garanzia,
			codiceProdotto = p_codiceProdotto, famigliaProdotto = p_famigliaProdotto, motivo = p_motivo,
			nomeCliente = p_nomeCliente, codiceCliente = p_codiceCliente, CF_PIVA = p_CF_PIVA,
			premioAnnuo = p_premioAnnuo, premioUnico = p_premioUnico, versamAgg = p_versamAgg, deltaPremio = p_deltaPremio, premioComputabile = p_premioComputabile,
			codiceCampagna = p_codiceCampagna, codiceConvenzione = p_codiceConvenzione, codicePartnership = p_codicePartnership,
			tipoCollettiva = p_tipoCollettiva, numPolizzaMadre = p_numPolizzaMadre, modulareVita = p_modulareVita
		;
	ELSE
		INSERT INTO iniz_polizze (
			iniziativa_id, ptf,
			agenzia_id, codEsazione,
			dataEstrazione, dataEmissione, dataIncasso, dataEffetto,
			polizza, delega, ramo, garanzia,
			codiceProdotto, famigliaProdotto, motivo,
			nomeCliente, codiceCliente, CF_PIVA,
			premioAnnuo, premioUnico, versamAgg, deltaPremio, premioComputabile,
			codiceCampagna, codiceConvenzione, codicePartnership, tipoCollettiva, numPolizzaMadre, modulareVita
		) VALUES (
			p_iniziativa_id, p_ptf,
			p_agenzia_id, p_codEsazione,
			p_dataEstrazione, p_dataEmissione, p_dataIncasso, p_dataEffetto,
			p_polizza, p_delega, p_ramo, p_garanzia,
			p_codiceProdotto, p_famigliaProdotto, p_motivo,
			p_nomeCliente, p_codiceCliente, p_CF_PIVA,
			p_premioAnnuo, p_premioUnico, p_versamAgg, p_deltaPremio, p_premioComputabile,
			p_codiceCampagna, p_codiceConvenzione, p_codicePartnership, p_tipoCollettiva, p_numPolizzaMadre, p_modulareVita
		) ON DUPLICATE KEY UPDATE
			iniziativa_id = p_iniziativa_id, ptf = p_ptf,
			codEsazione = p_codEsazione,
			agenzia_id = p_agenzia_id, dataEstrazione = p_dataEstrazione, dataEmissione = p_dataEmissione, dataIncasso = p_dataIncasso, dataEffetto = p_dataEffetto,
			polizza = p_polizza, delega = p_delega, ramo = p_ramo, garanzia = p_garanzia,
			codiceProdotto = p_codiceProdotto, famigliaProdotto = p_famigliaProdotto, motivo = p_motivo,
			nomeCliente = p_nomeCliente, codiceCliente = p_codiceCliente, CF_PIVA = p_CF_PIVA,
			premioAnnuo = p_premioAnnuo, premioUnico = p_premioUnico, versamAgg = p_versamAgg, deltaPremio = p_deltaPremio, premioComputabile = p_premioComputabile,
			codiceCampagna = p_codiceCampagna, codiceConvenzione = p_codiceConvenzione, codicePartnership = p_codicePartnership,
			tipoCollettiva = p_tipoCollettiva, numPolizzaMadre = p_numPolizzaMadre, modulareVita = p_modulareVita
		;
	END IF;
END; //
DELIMITER ;
