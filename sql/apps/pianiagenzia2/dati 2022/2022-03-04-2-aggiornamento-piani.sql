update pianiag2_piani set clusterX = SUBSTRING('3_3_4' from 1 for 1), clusterY = SUBSTRING('3_3_4' from 3 for 1), clusterZ = SUBSTRING('3_3_4' from 5),
                          cluster_id = case 'Astri' when '<PERSON><PERSON><PERSON>urate' then 1 when '<PERSON>tri' then 2 when '<PERSON><PERSON><PERSON>' then 3 when 'Fragili' then 4 end where agenzia_id = 'G002' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when '<PERSON><PERSON>' then 2 when '<PERSON>abi<PERSON>' then 3 when 'Fragili' then 4 end where agenzia_id = 'G012' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case '<PERSON>tri' when '<PERSON>ru<PERSON>ura<PERSON>' then 1 when '<PERSON><PERSON>' then 2 when '<PERSON>abi<PERSON>' then 3 when 'Fragili' then 4 end where agenzia_id = 'G014' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_8' from 1 for 1), clusterY = SUBSTRING('5_2_8' from 3 for 1), clusterZ = SUBSTRING('5_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G015' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G017' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_10' from 1 for 1), clusterY = SUBSTRING('5_5_10' from 3 for 1), clusterZ = SUBSTRING('5_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G019' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G020' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_4' from 1 for 1), clusterY = SUBSTRING('3_3_4' from 3 for 1), clusterZ = SUBSTRING('3_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G021' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_2' from 1 for 1), clusterY = SUBSTRING('2_3_2' from 3 for 1), clusterZ = SUBSTRING('2_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G023' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G025' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G026' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_10' from 1 for 1), clusterY = SUBSTRING('2_4_10' from 3 for 1), clusterZ = SUBSTRING('2_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G030' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_5' from 1 for 1), clusterY = SUBSTRING('1_2_5' from 3 for 1), clusterZ = SUBSTRING('1_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G031' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_3' from 1 for 1), clusterY = SUBSTRING('4_4_3' from 3 for 1), clusterZ = SUBSTRING('4_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G033' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_1' from 1 for 1), clusterY = SUBSTRING('3_4_1' from 3 for 1), clusterZ = SUBSTRING('3_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G034' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_1' from 1 for 1), clusterY = SUBSTRING('1_4_1' from 3 for 1), clusterZ = SUBSTRING('1_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G035' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_8' from 1 for 1), clusterY = SUBSTRING('1_1_8' from 3 for 1), clusterZ = SUBSTRING('1_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G036' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G040' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G041' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_10' from 1 for 1), clusterY = SUBSTRING('5_2_10' from 3 for 1), clusterZ = SUBSTRING('5_2_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G042' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_7' from 1 for 1), clusterY = SUBSTRING('1_4_7' from 3 for 1), clusterZ = SUBSTRING('1_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G043' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G045' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_7' from 1 for 1), clusterY = SUBSTRING('1_2_7' from 3 for 1), clusterZ = SUBSTRING('1_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G047' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G048' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_8' from 1 for 1), clusterY = SUBSTRING('1_1_8' from 3 for 1), clusterZ = SUBSTRING('1_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G050' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_1' from 1 for 1), clusterY = SUBSTRING('1_1_1' from 3 for 1), clusterZ = SUBSTRING('1_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G051' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G054' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G055' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G056' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G057' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_3' from 1 for 1), clusterY = SUBSTRING('4_3_3' from 3 for 1), clusterZ = SUBSTRING('4_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G058' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_7' from 1 for 1), clusterY = SUBSTRING('1_4_7' from 3 for 1), clusterZ = SUBSTRING('1_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G059' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_6' from 1 for 1), clusterY = SUBSTRING('5_1_6' from 3 for 1), clusterZ = SUBSTRING('5_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G061' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G063' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G064' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_8' from 1 for 1), clusterY = SUBSTRING('1_4_8' from 3 for 1), clusterZ = SUBSTRING('1_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G065' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_8' from 1 for 1), clusterY = SUBSTRING('1_4_8' from 3 for 1), clusterZ = SUBSTRING('1_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G066' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G067' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_9' from 1 for 1), clusterY = SUBSTRING('2_2_9' from 3 for 1), clusterZ = SUBSTRING('2_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G068' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G069' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_7' from 1 for 1), clusterY = SUBSTRING('2_2_7' from 3 for 1), clusterZ = SUBSTRING('2_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G070' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_7' from 1 for 1), clusterY = SUBSTRING('3_3_7' from 3 for 1), clusterZ = SUBSTRING('3_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G073' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_7' from 1 for 1), clusterY = SUBSTRING('2_3_7' from 3 for 1), clusterZ = SUBSTRING('2_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G074' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G075' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G077' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_5' from 1 for 1), clusterY = SUBSTRING('2_4_5' from 3 for 1), clusterZ = SUBSTRING('2_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G078' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_3' from 1 for 1), clusterY = SUBSTRING('5_3_3' from 3 for 1), clusterZ = SUBSTRING('5_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G079' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_9' from 1 for 1), clusterY = SUBSTRING('1_2_9' from 3 for 1), clusterZ = SUBSTRING('1_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G080' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G081' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_9' from 1 for 1), clusterY = SUBSTRING('4_2_9' from 3 for 1), clusterZ = SUBSTRING('4_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G089' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G091' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_6' from 1 for 1), clusterY = SUBSTRING('2_1_6' from 3 for 1), clusterZ = SUBSTRING('2_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G093' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_8' from 1 for 1), clusterY = SUBSTRING('1_4_8' from 3 for 1), clusterZ = SUBSTRING('1_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G098' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_7' from 1 for 1), clusterY = SUBSTRING('2_2_7' from 3 for 1), clusterZ = SUBSTRING('2_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G099' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G101' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G104' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G105' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_4' from 1 for 1), clusterY = SUBSTRING('1_4_4' from 3 for 1), clusterZ = SUBSTRING('1_4_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G106' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_10' from 1 for 1), clusterY = SUBSTRING('3_2_10' from 3 for 1), clusterZ = SUBSTRING('3_2_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G109' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_6' from 1 for 1), clusterY = SUBSTRING('2_2_6' from 3 for 1), clusterZ = SUBSTRING('2_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G110' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_3' from 1 for 1), clusterY = SUBSTRING('2_2_3' from 3 for 1), clusterZ = SUBSTRING('2_2_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G112' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_5' from 1 for 1), clusterY = SUBSTRING('2_1_5' from 3 for 1), clusterZ = SUBSTRING('2_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G114' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_9' from 1 for 1), clusterY = SUBSTRING('1_3_9' from 3 for 1), clusterZ = SUBSTRING('1_3_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G115' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_2' from 1 for 1), clusterY = SUBSTRING('1_3_2' from 3 for 1), clusterZ = SUBSTRING('1_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G116' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G117' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_5' from 1 for 1), clusterY = SUBSTRING('2_5_5' from 3 for 1), clusterZ = SUBSTRING('2_5_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G118' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_6' from 1 for 1), clusterY = SUBSTRING('1_5_6' from 3 for 1), clusterZ = SUBSTRING('1_5_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G119' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_9' from 1 for 1), clusterY = SUBSTRING('5_5_9' from 3 for 1), clusterZ = SUBSTRING('5_5_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G121' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G122' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_6' from 1 for 1), clusterY = SUBSTRING('3_4_6' from 3 for 1), clusterZ = SUBSTRING('3_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G123' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G124' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G125' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_10' from 1 for 1), clusterY = SUBSTRING('2_2_10' from 3 for 1), clusterZ = SUBSTRING('2_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G126' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_9' from 1 for 1), clusterY = SUBSTRING('4_2_9' from 3 for 1), clusterZ = SUBSTRING('4_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G130' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_5' from 1 for 1), clusterY = SUBSTRING('3_3_5' from 3 for 1), clusterZ = SUBSTRING('3_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G131' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_6' from 1 for 1), clusterY = SUBSTRING('1_4_6' from 3 for 1), clusterZ = SUBSTRING('1_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G132' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_5' from 1 for 1), clusterY = SUBSTRING('2_3_5' from 3 for 1), clusterZ = SUBSTRING('2_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G133' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_5' from 1 for 1), clusterY = SUBSTRING('4_2_5' from 3 for 1), clusterZ = SUBSTRING('4_2_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G134' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G136' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_8' from 1 for 1), clusterY = SUBSTRING('4_2_8' from 3 for 1), clusterZ = SUBSTRING('4_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G137' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_10' from 1 for 1), clusterY = SUBSTRING('1_3_10' from 3 for 1), clusterZ = SUBSTRING('1_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G138' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_1' from 1 for 1), clusterY = SUBSTRING('5_3_1' from 3 for 1), clusterZ = SUBSTRING('5_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G139' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_3' from 1 for 1), clusterY = SUBSTRING('4_2_3' from 3 for 1), clusterZ = SUBSTRING('4_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G140' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_5' from 1 for 1), clusterY = SUBSTRING('4_2_5' from 3 for 1), clusterZ = SUBSTRING('4_2_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G141' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_9' from 1 for 1), clusterY = SUBSTRING('4_3_9' from 3 for 1), clusterZ = SUBSTRING('4_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G143' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_1' from 1 for 1), clusterY = SUBSTRING('3_1_1' from 3 for 1), clusterZ = SUBSTRING('3_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G146' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_3' from 1 for 1), clusterY = SUBSTRING('1_2_3' from 3 for 1), clusterZ = SUBSTRING('1_2_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G150' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_4' from 1 for 1), clusterY = SUBSTRING('2_2_4' from 3 for 1), clusterZ = SUBSTRING('2_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G151' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_5' from 1 for 1), clusterY = SUBSTRING('2_4_5' from 3 for 1), clusterZ = SUBSTRING('2_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G159' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_3' from 1 for 1), clusterY = SUBSTRING('4_2_3' from 3 for 1), clusterZ = SUBSTRING('4_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G161' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_9' from 1 for 1), clusterY = SUBSTRING('1_1_9' from 3 for 1), clusterZ = SUBSTRING('1_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G162' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_10' from 1 for 1), clusterY = SUBSTRING('1_1_10' from 3 for 1), clusterZ = SUBSTRING('1_1_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G163' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_5' from 1 for 1), clusterY = SUBSTRING('2_3_5' from 3 for 1), clusterZ = SUBSTRING('2_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G166' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G167' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_8' from 1 for 1), clusterY = SUBSTRING('2_1_8' from 3 for 1), clusterZ = SUBSTRING('2_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G170' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_9' from 1 for 1), clusterY = SUBSTRING('1_4_9' from 3 for 1), clusterZ = SUBSTRING('1_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G171' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_2' from 1 for 1), clusterY = SUBSTRING('1_3_2' from 3 for 1), clusterZ = SUBSTRING('1_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G173' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_8' from 1 for 1), clusterY = SUBSTRING('1_2_8' from 3 for 1), clusterZ = SUBSTRING('1_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G174' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_8' from 1 for 1), clusterY = SUBSTRING('4_2_8' from 3 for 1), clusterZ = SUBSTRING('4_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G175' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G176' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_9' from 1 for 1), clusterY = SUBSTRING('2_2_9' from 3 for 1), clusterZ = SUBSTRING('2_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G177' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_6' from 1 for 1), clusterY = SUBSTRING('1_4_6' from 3 for 1), clusterZ = SUBSTRING('1_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G178' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_3' from 1 for 1), clusterY = SUBSTRING('3_3_3' from 3 for 1), clusterZ = SUBSTRING('3_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G179' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G180' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_10' from 1 for 1), clusterY = SUBSTRING('5_2_10' from 3 for 1), clusterZ = SUBSTRING('5_2_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G183' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G184' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_4' from 1 for 1), clusterY = SUBSTRING('3_4_4' from 3 for 1), clusterZ = SUBSTRING('3_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G187' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_3' from 1 for 1), clusterY = SUBSTRING('3_4_3' from 3 for 1), clusterZ = SUBSTRING('3_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G189' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G190' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G191' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G192' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_1' from 1 for 1), clusterY = SUBSTRING('2_2_1' from 3 for 1), clusterZ = SUBSTRING('2_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G193' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_3' from 1 for 1), clusterY = SUBSTRING('2_1_3' from 3 for 1), clusterZ = SUBSTRING('2_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G194' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_3' from 1 for 1), clusterY = SUBSTRING('4_5_3' from 3 for 1), clusterZ = SUBSTRING('4_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G195' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_3' from 1 for 1), clusterY = SUBSTRING('3_4_3' from 3 for 1), clusterZ = SUBSTRING('3_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G196' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G199' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_10' from 1 for 1), clusterY = SUBSTRING('4_3_10' from 3 for 1), clusterZ = SUBSTRING('4_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G200' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G201' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_5' from 1 for 1), clusterY = SUBSTRING('2_1_5' from 3 for 1), clusterZ = SUBSTRING('2_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G203' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G204' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_7' from 1 for 1), clusterY = SUBSTRING('5_5_7' from 3 for 1), clusterZ = SUBSTRING('5_5_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G208' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G209' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_7' from 1 for 1), clusterY = SUBSTRING('1_4_7' from 3 for 1), clusterZ = SUBSTRING('1_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G211' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G212' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_6' from 1 for 1), clusterY = SUBSTRING('2_1_6' from 3 for 1), clusterZ = SUBSTRING('2_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G213' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_7' from 1 for 1), clusterY = SUBSTRING('1_4_7' from 3 for 1), clusterZ = SUBSTRING('1_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G216' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G217' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G219' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G220' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_8' from 1 for 1), clusterY = SUBSTRING('5_4_8' from 3 for 1), clusterZ = SUBSTRING('5_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G221' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_9' from 1 for 1), clusterY = SUBSTRING('3_2_9' from 3 for 1), clusterZ = SUBSTRING('3_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G224' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_6' from 1 for 1), clusterY = SUBSTRING('4_3_6' from 3 for 1), clusterZ = SUBSTRING('4_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G225' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G226' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G227' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G230' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_7' from 1 for 1), clusterY = SUBSTRING('4_4_7' from 3 for 1), clusterZ = SUBSTRING('4_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G231' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_4' from 1 for 1), clusterY = SUBSTRING('4_4_4' from 3 for 1), clusterZ = SUBSTRING('4_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G233' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_10' from 1 for 1), clusterY = SUBSTRING('2_5_10' from 3 for 1), clusterZ = SUBSTRING('2_5_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G234' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_6' from 1 for 1), clusterY = SUBSTRING('5_5_6' from 3 for 1), clusterZ = SUBSTRING('5_5_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G235' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G238' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_4' from 1 for 1), clusterY = SUBSTRING('5_4_4' from 3 for 1), clusterZ = SUBSTRING('5_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G240' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_6' from 1 for 1), clusterY = SUBSTRING('3_2_6' from 3 for 1), clusterZ = SUBSTRING('3_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G242' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G243' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_9' from 1 for 1), clusterY = SUBSTRING('1_2_9' from 3 for 1), clusterZ = SUBSTRING('1_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G244' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G245' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_5' from 1 for 1), clusterY = SUBSTRING('2_2_5' from 3 for 1), clusterZ = SUBSTRING('2_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G248' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G249' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_9' from 1 for 1), clusterY = SUBSTRING('2_2_9' from 3 for 1), clusterZ = SUBSTRING('2_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G252' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_9' from 1 for 1), clusterY = SUBSTRING('1_4_9' from 3 for 1), clusterZ = SUBSTRING('1_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G253' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_6' from 1 for 1), clusterY = SUBSTRING('2_3_6' from 3 for 1), clusterZ = SUBSTRING('2_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G254' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G255' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G256' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_3' from 1 for 1), clusterY = SUBSTRING('3_1_3' from 3 for 1), clusterZ = SUBSTRING('3_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G257' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G258' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_6' from 1 for 1), clusterY = SUBSTRING('5_4_6' from 3 for 1), clusterZ = SUBSTRING('5_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G260' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_1' from 1 for 1), clusterY = SUBSTRING('2_3_1' from 3 for 1), clusterZ = SUBSTRING('2_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G261' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_6' from 1 for 1), clusterY = SUBSTRING('3_4_6' from 3 for 1), clusterZ = SUBSTRING('3_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G262' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G264' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G266' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G267' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_1' from 1 for 1), clusterY = SUBSTRING('2_4_1' from 3 for 1), clusterZ = SUBSTRING('2_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G268' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G269' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G270' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_5' from 1 for 1), clusterY = SUBSTRING('4_1_5' from 3 for 1), clusterZ = SUBSTRING('4_1_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G273' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_9' from 1 for 1), clusterY = SUBSTRING('1_2_9' from 3 for 1), clusterZ = SUBSTRING('1_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G276' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G277' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G278' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G280' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_6' from 1 for 1), clusterY = SUBSTRING('5_4_6' from 3 for 1), clusterZ = SUBSTRING('5_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G281' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G282' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G286' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_9' from 1 for 1), clusterY = SUBSTRING('1_1_9' from 3 for 1), clusterZ = SUBSTRING('1_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G287' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_7' from 1 for 1), clusterY = SUBSTRING('1_2_7' from 3 for 1), clusterZ = SUBSTRING('1_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G290' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_9' from 1 for 1), clusterY = SUBSTRING('4_3_9' from 3 for 1), clusterZ = SUBSTRING('4_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G291' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G293' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_4' from 1 for 1), clusterY = SUBSTRING('2_4_4' from 3 for 1), clusterZ = SUBSTRING('2_4_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G294' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G295' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G296' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_10' from 1 for 1), clusterY = SUBSTRING('3_5_10' from 3 for 1), clusterZ = SUBSTRING('3_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G297' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_7' from 1 for 1), clusterY = SUBSTRING('5_2_7' from 3 for 1), clusterZ = SUBSTRING('5_2_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G298' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_3' from 1 for 1), clusterY = SUBSTRING('3_4_3' from 3 for 1), clusterZ = SUBSTRING('3_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G303' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_4' from 1 for 1), clusterY = SUBSTRING('3_1_4' from 3 for 1), clusterZ = SUBSTRING('3_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G304' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G306' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G308' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_8' from 1 for 1), clusterY = SUBSTRING('4_4_8' from 3 for 1), clusterZ = SUBSTRING('4_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G312' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_4' from 1 for 1), clusterY = SUBSTRING('4_4_4' from 3 for 1), clusterZ = SUBSTRING('4_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G314' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_6' from 1 for 1), clusterY = SUBSTRING('4_2_6' from 3 for 1), clusterZ = SUBSTRING('4_2_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G315' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G318' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G319' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G320' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_6' from 1 for 1), clusterY = SUBSTRING('1_4_6' from 3 for 1), clusterZ = SUBSTRING('1_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G321' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G322' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_7' from 1 for 1), clusterY = SUBSTRING('4_4_7' from 3 for 1), clusterZ = SUBSTRING('4_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G324' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_1' from 1 for 1), clusterY = SUBSTRING('5_4_1' from 3 for 1), clusterZ = SUBSTRING('5_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G325' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G328' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_3' from 1 for 1), clusterY = SUBSTRING('5_3_3' from 3 for 1), clusterZ = SUBSTRING('5_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G329' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_4' from 1 for 1), clusterY = SUBSTRING('3_4_4' from 3 for 1), clusterZ = SUBSTRING('3_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G333' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_4' from 1 for 1), clusterY = SUBSTRING('4_3_4' from 3 for 1), clusterZ = SUBSTRING('4_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G334' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G335' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_5' from 1 for 1), clusterY = SUBSTRING('4_3_5' from 3 for 1), clusterZ = SUBSTRING('4_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G336' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_9' from 1 for 1), clusterY = SUBSTRING('1_4_9' from 3 for 1), clusterZ = SUBSTRING('1_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G337' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_4' from 1 for 1), clusterY = SUBSTRING('5_3_4' from 3 for 1), clusterZ = SUBSTRING('5_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G338' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G339' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_2' from 1 for 1), clusterY = SUBSTRING('5_2_2' from 3 for 1), clusterZ = SUBSTRING('5_2_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G342' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G343' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G344' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_7' from 1 for 1), clusterY = SUBSTRING('2_2_7' from 3 for 1), clusterZ = SUBSTRING('2_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G351' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_1' from 1 for 1), clusterY = SUBSTRING('5_3_1' from 3 for 1), clusterZ = SUBSTRING('5_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G352' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_3' from 1 for 1), clusterY = SUBSTRING('3_3_3' from 3 for 1), clusterZ = SUBSTRING('3_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G355' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_7' from 1 for 1), clusterY = SUBSTRING('1_5_7' from 3 for 1), clusterZ = SUBSTRING('1_5_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G356' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_7' from 1 for 1), clusterY = SUBSTRING('2_4_7' from 3 for 1), clusterZ = SUBSTRING('2_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G358' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_5' from 1 for 1), clusterY = SUBSTRING('4_3_5' from 3 for 1), clusterZ = SUBSTRING('4_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G359' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_5' from 1 for 1), clusterY = SUBSTRING('2_2_5' from 3 for 1), clusterZ = SUBSTRING('2_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G360' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_1' from 1 for 1), clusterY = SUBSTRING('1_4_1' from 3 for 1), clusterZ = SUBSTRING('1_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G371' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_1' from 1 for 1), clusterY = SUBSTRING('1_3_1' from 3 for 1), clusterZ = SUBSTRING('1_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G373' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_4' from 1 for 1), clusterY = SUBSTRING('3_1_4' from 3 for 1), clusterZ = SUBSTRING('3_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G375' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G376' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G379' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_6' from 1 for 1), clusterY = SUBSTRING('1_3_6' from 3 for 1), clusterZ = SUBSTRING('1_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G380' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_3' from 1 for 1), clusterY = SUBSTRING('5_3_3' from 3 for 1), clusterZ = SUBSTRING('5_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G382' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G385' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_2' from 1 for 1), clusterY = SUBSTRING('2_2_2' from 3 for 1), clusterZ = SUBSTRING('2_2_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G389' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_7' from 1 for 1), clusterY = SUBSTRING('4_4_7' from 3 for 1), clusterZ = SUBSTRING('4_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G390' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G406' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G407' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_3' from 1 for 1), clusterY = SUBSTRING('2_3_3' from 3 for 1), clusterZ = SUBSTRING('2_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G408' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_8' from 1 for 1), clusterY = SUBSTRING('2_1_8' from 3 for 1), clusterZ = SUBSTRING('2_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G409' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_1' from 1 for 1), clusterY = SUBSTRING('2_3_1' from 3 for 1), clusterZ = SUBSTRING('2_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G410' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_8' from 1 for 1), clusterY = SUBSTRING('1_2_8' from 3 for 1), clusterZ = SUBSTRING('1_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G411' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G413' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G414' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G415' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_3' from 1 for 1), clusterY = SUBSTRING('5_1_3' from 3 for 1), clusterZ = SUBSTRING('5_1_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G419' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_5' from 1 for 1), clusterY = SUBSTRING('2_2_5' from 3 for 1), clusterZ = SUBSTRING('2_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G420' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_8' from 1 for 1), clusterY = SUBSTRING('3_2_8' from 3 for 1), clusterZ = SUBSTRING('3_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G421' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G423' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_1' from 1 for 1), clusterY = SUBSTRING('3_2_1' from 3 for 1), clusterZ = SUBSTRING('3_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G426' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_3' from 1 for 1), clusterY = SUBSTRING('3_4_3' from 3 for 1), clusterZ = SUBSTRING('3_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G427' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_9' from 1 for 1), clusterY = SUBSTRING('4_4_9' from 3 for 1), clusterZ = SUBSTRING('4_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G428' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_8' from 1 for 1), clusterY = SUBSTRING('1_1_8' from 3 for 1), clusterZ = SUBSTRING('1_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G429' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_3' from 1 for 1), clusterY = SUBSTRING('2_3_3' from 3 for 1), clusterZ = SUBSTRING('2_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G431' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G432' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G434' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G435' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G436' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G437' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G441' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G442' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G443' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_2' from 1 for 1), clusterY = SUBSTRING('5_5_2' from 3 for 1), clusterZ = SUBSTRING('5_5_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G446' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G448' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G450' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_6' from 1 for 1), clusterY = SUBSTRING('3_4_6' from 3 for 1), clusterZ = SUBSTRING('3_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G451' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_2' from 1 for 1), clusterY = SUBSTRING('1_1_2' from 3 for 1), clusterZ = SUBSTRING('1_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G454' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_3' from 1 for 1), clusterY = SUBSTRING('1_2_3' from 3 for 1), clusterZ = SUBSTRING('1_2_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G455' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G457' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_8' from 1 for 1), clusterY = SUBSTRING('2_1_8' from 3 for 1), clusterZ = SUBSTRING('2_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G458' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_6' from 1 for 1), clusterY = SUBSTRING('4_3_6' from 3 for 1), clusterZ = SUBSTRING('4_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G464' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G465' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G466' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_8' from 1 for 1), clusterY = SUBSTRING('2_2_8' from 3 for 1), clusterZ = SUBSTRING('2_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G468' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_6' from 1 for 1), clusterY = SUBSTRING('4_2_6' from 3 for 1), clusterZ = SUBSTRING('4_2_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G469' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_7' from 1 for 1), clusterY = SUBSTRING('1_5_7' from 3 for 1), clusterZ = SUBSTRING('1_5_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G471' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_1' from 1 for 1), clusterY = SUBSTRING('2_2_1' from 3 for 1), clusterZ = SUBSTRING('2_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G472' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_5' from 1 for 1), clusterY = SUBSTRING('2_3_5' from 3 for 1), clusterZ = SUBSTRING('2_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G473' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G474' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G476' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G478' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_1' from 1 for 1), clusterY = SUBSTRING('3_5_1' from 3 for 1), clusterZ = SUBSTRING('3_5_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G479' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_8' from 1 for 1), clusterY = SUBSTRING('2_3_8' from 3 for 1), clusterZ = SUBSTRING('2_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G481' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_5' from 1 for 1), clusterY = SUBSTRING('3_5_5' from 3 for 1), clusterZ = SUBSTRING('3_5_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G482' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_8' from 1 for 1), clusterY = SUBSTRING('1_3_8' from 3 for 1), clusterZ = SUBSTRING('1_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G484' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_8' from 1 for 1), clusterY = SUBSTRING('2_5_8' from 3 for 1), clusterZ = SUBSTRING('2_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G485' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G486' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G487' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_7' from 1 for 1), clusterY = SUBSTRING('4_3_7' from 3 for 1), clusterZ = SUBSTRING('4_3_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G491' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_4' from 1 for 1), clusterY = SUBSTRING('3_3_4' from 3 for 1), clusterZ = SUBSTRING('3_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G492' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_6' from 1 for 1), clusterY = SUBSTRING('5_2_6' from 3 for 1), clusterZ = SUBSTRING('5_2_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G536' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_5' from 1 for 1), clusterY = SUBSTRING('5_2_5' from 3 for 1), clusterZ = SUBSTRING('5_2_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G541' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_10' from 1 for 1), clusterY = SUBSTRING('1_3_10' from 3 for 1), clusterZ = SUBSTRING('1_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G542' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_10' from 1 for 1), clusterY = SUBSTRING('2_1_10' from 3 for 1), clusterZ = SUBSTRING('2_1_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G548' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G550' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_10' from 1 for 1), clusterY = SUBSTRING('5_1_10' from 3 for 1), clusterZ = SUBSTRING('5_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G560' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_9' from 1 for 1), clusterY = SUBSTRING('2_2_9' from 3 for 1), clusterZ = SUBSTRING('2_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G598' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G602' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_2' from 1 for 1), clusterY = SUBSTRING('5_2_2' from 3 for 1), clusterZ = SUBSTRING('5_2_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G605' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G607' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_7' from 1 for 1), clusterY = SUBSTRING('3_2_7' from 3 for 1), clusterZ = SUBSTRING('3_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G630' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_3' from 1 for 1), clusterY = SUBSTRING('3_1_3' from 3 for 1), clusterZ = SUBSTRING('3_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G637' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_9' from 1 for 1), clusterY = SUBSTRING('5_3_9' from 3 for 1), clusterZ = SUBSTRING('5_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G640' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G651' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G661' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_10' from 1 for 1), clusterY = SUBSTRING('4_1_10' from 3 for 1), clusterZ = SUBSTRING('4_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G764' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_7' from 1 for 1), clusterY = SUBSTRING('3_3_7' from 3 for 1), clusterZ = SUBSTRING('3_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G765' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_9' from 1 for 1), clusterY = SUBSTRING('2_1_9' from 3 for 1), clusterZ = SUBSTRING('2_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G766' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_5' from 1 for 1), clusterY = SUBSTRING('3_2_5' from 3 for 1), clusterZ = SUBSTRING('3_2_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G771' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G772' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_10' from 1 for 1), clusterY = SUBSTRING('5_3_10' from 3 for 1), clusterZ = SUBSTRING('5_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G774' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_3' from 1 for 1), clusterY = SUBSTRING('2_1_3' from 3 for 1), clusterZ = SUBSTRING('2_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G801' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G804' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G805' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G806' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G808' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G812' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G813' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G814' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_1' from 1 for 1), clusterY = SUBSTRING('2_3_1' from 3 for 1), clusterZ = SUBSTRING('2_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G816' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_6' from 1 for 1), clusterY = SUBSTRING('4_1_6' from 3 for 1), clusterZ = SUBSTRING('4_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G817' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G818' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_8' from 1 for 1), clusterY = SUBSTRING('2_1_8' from 3 for 1), clusterZ = SUBSTRING('2_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G819' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_6' from 1 for 1), clusterY = SUBSTRING('1_1_6' from 3 for 1), clusterZ = SUBSTRING('1_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G820' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G822' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G827' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G831' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_4' from 1 for 1), clusterY = SUBSTRING('5_4_4' from 3 for 1), clusterZ = SUBSTRING('5_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G832' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G834' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_2' from 1 for 1), clusterY = SUBSTRING('1_4_2' from 3 for 1), clusterZ = SUBSTRING('1_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G835' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G837' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_9' from 1 for 1), clusterY = SUBSTRING('4_1_9' from 3 for 1), clusterZ = SUBSTRING('4_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G841' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_9' from 1 for 1), clusterY = SUBSTRING('5_5_9' from 3 for 1), clusterZ = SUBSTRING('5_5_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G842' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_4' from 1 for 1), clusterY = SUBSTRING('2_4_4' from 3 for 1), clusterZ = SUBSTRING('2_4_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G843' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_9' from 1 for 1), clusterY = SUBSTRING('2_5_9' from 3 for 1), clusterZ = SUBSTRING('2_5_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G845' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_5' from 1 for 1), clusterY = SUBSTRING('5_4_5' from 3 for 1), clusterZ = SUBSTRING('5_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G847' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_7' from 1 for 1), clusterY = SUBSTRING('3_1_7' from 3 for 1), clusterZ = SUBSTRING('3_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G848' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_6' from 1 for 1), clusterY = SUBSTRING('2_3_6' from 3 for 1), clusterZ = SUBSTRING('2_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G849' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G850' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_5' from 1 for 1), clusterY = SUBSTRING('3_3_5' from 3 for 1), clusterZ = SUBSTRING('3_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G851' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_8' from 1 for 1), clusterY = SUBSTRING('3_5_8' from 3 for 1), clusterZ = SUBSTRING('3_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G852' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G853' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_10' from 1 for 1), clusterY = SUBSTRING('4_2_10' from 3 for 1), clusterZ = SUBSTRING('4_2_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G854' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_5' from 1 for 1), clusterY = SUBSTRING('4_1_5' from 3 for 1), clusterZ = SUBSTRING('4_1_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G855' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_3' from 1 for 1), clusterY = SUBSTRING('1_1_3' from 3 for 1), clusterZ = SUBSTRING('1_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G857' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G858' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_5' from 1 for 1), clusterY = SUBSTRING('2_4_5' from 3 for 1), clusterZ = SUBSTRING('2_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G860' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_9' from 1 for 1), clusterY = SUBSTRING('3_2_9' from 3 for 1), clusterZ = SUBSTRING('3_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G861' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_3' from 1 for 1), clusterY = SUBSTRING('1_3_3' from 3 for 1), clusterZ = SUBSTRING('1_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G862' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G863' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_6' from 1 for 1), clusterY = SUBSTRING('2_2_6' from 3 for 1), clusterZ = SUBSTRING('2_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G864' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_3' from 1 for 1), clusterY = SUBSTRING('5_2_3' from 3 for 1), clusterZ = SUBSTRING('5_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G865' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_4' from 1 for 1), clusterY = SUBSTRING('2_4_4' from 3 for 1), clusterZ = SUBSTRING('2_4_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G868' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_4' from 1 for 1), clusterY = SUBSTRING('4_3_4' from 3 for 1), clusterZ = SUBSTRING('4_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G870' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G872' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_6' from 1 for 1), clusterY = SUBSTRING('4_3_6' from 3 for 1), clusterZ = SUBSTRING('4_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G874' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G875' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G876' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_4' from 1 for 1), clusterY = SUBSTRING('4_3_4' from 3 for 1), clusterZ = SUBSTRING('4_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G878' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G887' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_2' from 1 for 1), clusterY = SUBSTRING('5_4_2' from 3 for 1), clusterZ = SUBSTRING('5_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G890' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_7' from 1 for 1), clusterY = SUBSTRING('4_1_7' from 3 for 1), clusterZ = SUBSTRING('4_1_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G892' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_8' from 1 for 1), clusterY = SUBSTRING('5_3_8' from 3 for 1), clusterZ = SUBSTRING('5_3_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G894' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G895' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_8' from 1 for 1), clusterY = SUBSTRING('5_4_8' from 3 for 1), clusterZ = SUBSTRING('5_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G898' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G902' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_3' from 1 for 1), clusterY = SUBSTRING('2_1_3' from 3 for 1), clusterZ = SUBSTRING('2_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G906' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_8' from 1 for 1), clusterY = SUBSTRING('4_1_8' from 3 for 1), clusterZ = SUBSTRING('4_1_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G907' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_6' from 1 for 1), clusterY = SUBSTRING('3_2_6' from 3 for 1), clusterZ = SUBSTRING('3_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G911' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G912' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_5' from 1 for 1), clusterY = SUBSTRING('2_2_5' from 3 for 1), clusterZ = SUBSTRING('2_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G913' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_1' from 1 for 1), clusterY = SUBSTRING('3_2_1' from 3 for 1), clusterZ = SUBSTRING('3_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G914' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_1' from 1 for 1), clusterY = SUBSTRING('5_3_1' from 3 for 1), clusterZ = SUBSTRING('5_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G920' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G922' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_9' from 1 for 1), clusterY = SUBSTRING('2_1_9' from 3 for 1), clusterZ = SUBSTRING('2_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G924' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_2' from 1 for 1), clusterY = SUBSTRING('2_2_2' from 3 for 1), clusterZ = SUBSTRING('2_2_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G927' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G928' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_4' from 1 for 1), clusterY = SUBSTRING('4_2_4' from 3 for 1), clusterZ = SUBSTRING('4_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G929' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_10' from 1 for 1), clusterY = SUBSTRING('4_3_10' from 3 for 1), clusterZ = SUBSTRING('4_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G930' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G931' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_8' from 1 for 1), clusterY = SUBSTRING('2_1_8' from 3 for 1), clusterZ = SUBSTRING('2_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G933' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_1' from 1 for 1), clusterY = SUBSTRING('3_1_1' from 3 for 1), clusterZ = SUBSTRING('3_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G935' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_2' from 1 for 1), clusterY = SUBSTRING('2_3_2' from 3 for 1), clusterZ = SUBSTRING('2_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G936' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G937' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G938' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_4' from 1 for 1), clusterY = SUBSTRING('4_2_4' from 3 for 1), clusterZ = SUBSTRING('4_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G940' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G943' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_2' from 1 for 1), clusterY = SUBSTRING('4_3_2' from 3 for 1), clusterZ = SUBSTRING('4_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G946' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_7' from 1 for 1), clusterY = SUBSTRING('5_2_7' from 3 for 1), clusterZ = SUBSTRING('5_2_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G947' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_8' from 1 for 1), clusterY = SUBSTRING('2_4_8' from 3 for 1), clusterZ = SUBSTRING('2_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G949' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_3' from 1 for 1), clusterY = SUBSTRING('2_3_3' from 3 for 1), clusterZ = SUBSTRING('2_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G950' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_8' from 1 for 1), clusterY = SUBSTRING('2_4_8' from 3 for 1), clusterZ = SUBSTRING('2_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G952' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G953' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_5' from 1 for 1), clusterY = SUBSTRING('3_5_5' from 3 for 1), clusterZ = SUBSTRING('3_5_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G954' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_3' from 1 for 1), clusterY = SUBSTRING('4_2_3' from 3 for 1), clusterZ = SUBSTRING('4_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G957' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_8' from 1 for 1), clusterY = SUBSTRING('1_2_8' from 3 for 1), clusterZ = SUBSTRING('1_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G958' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G959' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_6' from 1 for 1), clusterY = SUBSTRING('3_3_6' from 3 for 1), clusterZ = SUBSTRING('3_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G960' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_6' from 1 for 1), clusterY = SUBSTRING('5_2_6' from 3 for 1), clusterZ = SUBSTRING('5_2_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G961' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_10' from 1 for 1), clusterY = SUBSTRING('1_4_10' from 3 for 1), clusterZ = SUBSTRING('1_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G962' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G963' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G966' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_10' from 1 for 1), clusterY = SUBSTRING('4_5_10' from 3 for 1), clusterZ = SUBSTRING('4_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G967' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G968' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_6' from 1 for 1), clusterY = SUBSTRING('2_3_6' from 3 for 1), clusterZ = SUBSTRING('2_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G969' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_1' from 1 for 1), clusterY = SUBSTRING('3_1_1' from 3 for 1), clusterZ = SUBSTRING('3_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G970' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_2' from 1 for 1), clusterY = SUBSTRING('2_4_2' from 3 for 1), clusterZ = SUBSTRING('2_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G971' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_6' from 1 for 1), clusterY = SUBSTRING('2_4_6' from 3 for 1), clusterZ = SUBSTRING('2_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G972' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_8' from 1 for 1), clusterY = SUBSTRING('3_3_8' from 3 for 1), clusterZ = SUBSTRING('3_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G977' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_6' from 1 for 1), clusterY = SUBSTRING('2_5_6' from 3 for 1), clusterZ = SUBSTRING('2_5_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G978' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_2' from 1 for 1), clusterY = SUBSTRING('1_1_2' from 3 for 1), clusterZ = SUBSTRING('1_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G980' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G981' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_10' from 1 for 1), clusterY = SUBSTRING('3_5_10' from 3 for 1), clusterZ = SUBSTRING('3_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G982' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_9' from 1 for 1), clusterY = SUBSTRING('1_2_9' from 3 for 1), clusterZ = SUBSTRING('1_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G983' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_7' from 1 for 1), clusterY = SUBSTRING('3_2_7' from 3 for 1), clusterZ = SUBSTRING('3_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G985' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_5' from 1 for 1), clusterY = SUBSTRING('5_3_5' from 3 for 1), clusterZ = SUBSTRING('5_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G987' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_5' from 1 for 1), clusterY = SUBSTRING('2_4_5' from 3 for 1), clusterZ = SUBSTRING('2_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G988' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_4' from 1 for 1), clusterY = SUBSTRING('5_3_4' from 3 for 1), clusterZ = SUBSTRING('5_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G989' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_6' from 1 for 1), clusterY = SUBSTRING('5_5_6' from 3 for 1), clusterZ = SUBSTRING('5_5_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G991' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_2' from 1 for 1), clusterY = SUBSTRING('5_1_2' from 3 for 1), clusterZ = SUBSTRING('5_1_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G992' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_7' from 1 for 1), clusterY = SUBSTRING('1_1_7' from 3 for 1), clusterZ = SUBSTRING('1_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G993' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G994' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_5' from 1 for 1), clusterY = SUBSTRING('4_4_5' from 3 for 1), clusterZ = SUBSTRING('4_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'G996' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GK80' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GK81' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GK82' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL03' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_4' from 1 for 1), clusterY = SUBSTRING('1_1_4' from 3 for 1), clusterZ = SUBSTRING('1_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL04' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_7' from 1 for 1), clusterY = SUBSTRING('4_2_7' from 3 for 1), clusterZ = SUBSTRING('4_2_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL05' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_3' from 1 for 1), clusterY = SUBSTRING('5_3_3' from 3 for 1), clusterZ = SUBSTRING('5_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL06' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_3' from 1 for 1), clusterY = SUBSTRING('4_4_3' from 3 for 1), clusterZ = SUBSTRING('4_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL07' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_9' from 1 for 1), clusterY = SUBSTRING('5_2_9' from 3 for 1), clusterZ = SUBSTRING('5_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL08' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_10' from 1 for 1), clusterY = SUBSTRING('4_1_10' from 3 for 1), clusterZ = SUBSTRING('4_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL11' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL12' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_8' from 1 for 1), clusterY = SUBSTRING('5_3_8' from 3 for 1), clusterZ = SUBSTRING('5_3_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL14' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_9' from 1 for 1), clusterY = SUBSTRING('4_2_9' from 3 for 1), clusterZ = SUBSTRING('4_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL15' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_4' from 1 for 1), clusterY = SUBSTRING('4_4_4' from 3 for 1), clusterZ = SUBSTRING('4_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL16' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL18' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_3' from 1 for 1), clusterY = SUBSTRING('4_2_3' from 3 for 1), clusterZ = SUBSTRING('4_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL19' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_7' from 1 for 1), clusterY = SUBSTRING('5_1_7' from 3 for 1), clusterZ = SUBSTRING('5_1_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL20' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL21' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_4' from 1 for 1), clusterY = SUBSTRING('5_2_4' from 3 for 1), clusterZ = SUBSTRING('5_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL22' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_8' from 1 for 1), clusterY = SUBSTRING('1_5_8' from 3 for 1), clusterZ = SUBSTRING('1_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL23' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_2' from 1 for 1), clusterY = SUBSTRING('5_1_2' from 3 for 1), clusterZ = SUBSTRING('5_1_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL24' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_6' from 1 for 1), clusterY = SUBSTRING('5_3_6' from 3 for 1), clusterZ = SUBSTRING('5_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL25' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_9' from 1 for 1), clusterY = SUBSTRING('5_1_9' from 3 for 1), clusterZ = SUBSTRING('5_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL26' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_3' from 1 for 1), clusterY = SUBSTRING('1_3_3' from 3 for 1), clusterZ = SUBSTRING('1_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL27' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_1' from 1 for 1), clusterY = SUBSTRING('5_2_1' from 3 for 1), clusterZ = SUBSTRING('5_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL28' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_9' from 1 for 1), clusterY = SUBSTRING('4_1_9' from 3 for 1), clusterZ = SUBSTRING('4_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL31' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL32' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_8' from 1 for 1), clusterY = SUBSTRING('5_2_8' from 3 for 1), clusterZ = SUBSTRING('5_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL33' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_5' from 1 for 1), clusterY = SUBSTRING('2_1_5' from 3 for 1), clusterZ = SUBSTRING('2_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL38' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_6' from 1 for 1), clusterY = SUBSTRING('5_2_6' from 3 for 1), clusterZ = SUBSTRING('5_2_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL41' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_5' from 1 for 1), clusterY = SUBSTRING('5_3_5' from 3 for 1), clusterZ = SUBSTRING('5_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL43' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_10' from 1 for 1), clusterY = SUBSTRING('2_1_10' from 3 for 1), clusterZ = SUBSTRING('2_1_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL44' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_9' from 1 for 1), clusterY = SUBSTRING('4_2_9' from 3 for 1), clusterZ = SUBSTRING('4_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL45' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL46' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_4' from 1 for 1), clusterY = SUBSTRING('2_2_4' from 3 for 1), clusterZ = SUBSTRING('2_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL47' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL48' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_10' from 1 for 1), clusterY = SUBSTRING('3_3_10' from 3 for 1), clusterZ = SUBSTRING('3_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL49' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_6' from 1 for 1), clusterY = SUBSTRING('3_1_6' from 3 for 1), clusterZ = SUBSTRING('3_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL50' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_10' from 1 for 1), clusterY = SUBSTRING('4_1_10' from 3 for 1), clusterZ = SUBSTRING('4_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL51' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_10' from 1 for 1), clusterY = SUBSTRING('2_2_10' from 3 for 1), clusterZ = SUBSTRING('2_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL52' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL54' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL55' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_3' from 1 for 1), clusterY = SUBSTRING('5_2_3' from 3 for 1), clusterZ = SUBSTRING('5_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL56' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_9' from 1 for 1), clusterY = SUBSTRING('5_2_9' from 3 for 1), clusterZ = SUBSTRING('5_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL57' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_10' from 1 for 1), clusterY = SUBSTRING('4_1_10' from 3 for 1), clusterZ = SUBSTRING('4_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL58' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_2' from 1 for 1), clusterY = SUBSTRING('1_1_2' from 3 for 1), clusterZ = SUBSTRING('1_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL60' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_4' from 1 for 1), clusterY = SUBSTRING('2_1_4' from 3 for 1), clusterZ = SUBSTRING('2_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL62' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_4' from 1 for 1), clusterY = SUBSTRING('3_1_4' from 3 for 1), clusterZ = SUBSTRING('3_1_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL63' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_7' from 1 for 1), clusterY = SUBSTRING('4_1_7' from 3 for 1), clusterZ = SUBSTRING('4_1_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL64' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_8' from 1 for 1), clusterY = SUBSTRING('5_2_8' from 3 for 1), clusterZ = SUBSTRING('5_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL65' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL67' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_1' from 1 for 1), clusterY = SUBSTRING('4_1_1' from 3 for 1), clusterZ = SUBSTRING('4_1_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL70' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_6' from 1 for 1), clusterY = SUBSTRING('5_1_6' from 3 for 1), clusterZ = SUBSTRING('5_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL71' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_6' from 1 for 1), clusterY = SUBSTRING('1_1_6' from 3 for 1), clusterZ = SUBSTRING('1_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL72' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_4' from 1 for 1), clusterY = SUBSTRING('5_1_4' from 3 for 1), clusterZ = SUBSTRING('5_1_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL73' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL75' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_6' from 1 for 1), clusterY = SUBSTRING('5_1_6' from 3 for 1), clusterZ = SUBSTRING('5_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL76' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL77' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_1' from 1 for 1), clusterY = SUBSTRING('5_1_1' from 3 for 1), clusterZ = SUBSTRING('5_1_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL78' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_2' from 1 for 1), clusterY = SUBSTRING('5_1_2' from 3 for 1), clusterZ = SUBSTRING('5_1_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL79' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_5' from 1 for 1), clusterY = SUBSTRING('3_1_5' from 3 for 1), clusterZ = SUBSTRING('3_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL80' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_5' from 1 for 1), clusterY = SUBSTRING('3_1_5' from 3 for 1), clusterZ = SUBSTRING('3_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL81' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_6' from 1 for 1), clusterY = SUBSTRING('5_1_6' from 3 for 1), clusterZ = SUBSTRING('5_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL82' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_7' from 1 for 1), clusterY = SUBSTRING('5_1_7' from 3 for 1), clusterZ = SUBSTRING('5_1_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL83' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_6' from 1 for 1), clusterY = SUBSTRING('4_1_6' from 3 for 1), clusterZ = SUBSTRING('4_1_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL84' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_1' from 1 for 1), clusterY = SUBSTRING('5_1_1' from 3 for 1), clusterZ = SUBSTRING('5_1_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL85' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_10' from 1 for 1), clusterY = SUBSTRING('5_1_10' from 3 for 1), clusterZ = SUBSTRING('5_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL88' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_10' from 1 for 1), clusterY = SUBSTRING('5_1_10' from 3 for 1), clusterZ = SUBSTRING('5_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL89' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_5' from 1 for 1), clusterY = SUBSTRING('4_1_5' from 3 for 1), clusterZ = SUBSTRING('4_1_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL90' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL91' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_7' from 1 for 1), clusterY = SUBSTRING('5_4_7' from 3 for 1), clusterZ = SUBSTRING('5_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL92' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_10' from 1 for 1), clusterY = SUBSTRING('5_1_10' from 3 for 1), clusterZ = SUBSTRING('5_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GL95' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_8' from 1 for 1), clusterY = SUBSTRING('1_5_8' from 3 for 1), clusterZ = SUBSTRING('1_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM09' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM10' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_1' from 1 for 1), clusterY = SUBSTRING('1_5_1' from 3 for 1), clusterZ = SUBSTRING('1_5_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM11' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_5' from 1 for 1), clusterY = SUBSTRING('3_5_5' from 3 for 1), clusterZ = SUBSTRING('3_5_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM13' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_7' from 1 for 1), clusterY = SUBSTRING('1_5_7' from 3 for 1), clusterZ = SUBSTRING('1_5_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM18' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_1' from 1 for 1), clusterY = SUBSTRING('3_5_1' from 3 for 1), clusterZ = SUBSTRING('3_5_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM21' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GM25' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_1' from 1 for 1), clusterY = SUBSTRING('5_5_1' from 3 for 1), clusterZ = SUBSTRING('5_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'GX01' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_3' from 1 for 1), clusterY = SUBSTRING('1_4_3' from 3 for 1), clusterZ = SUBSTRING('1_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N001' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_2' from 1 for 1), clusterY = SUBSTRING('3_2_2' from 3 for 1), clusterZ = SUBSTRING('3_2_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N002' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_6' from 1 for 1), clusterY = SUBSTRING('2_2_6' from 3 for 1), clusterZ = SUBSTRING('2_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N005' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_8' from 1 for 1), clusterY = SUBSTRING('1_4_8' from 3 for 1), clusterZ = SUBSTRING('1_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N006' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_5' from 1 for 1), clusterY = SUBSTRING('1_4_5' from 3 for 1), clusterZ = SUBSTRING('1_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N008' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_9' from 1 for 1), clusterY = SUBSTRING('1_4_9' from 3 for 1), clusterZ = SUBSTRING('1_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N009' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N014' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_8' from 1 for 1), clusterY = SUBSTRING('1_2_8' from 3 for 1), clusterZ = SUBSTRING('1_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N030' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_2' from 1 for 1), clusterY = SUBSTRING('3_1_2' from 3 for 1), clusterZ = SUBSTRING('3_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N033' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_4' from 1 for 1), clusterY = SUBSTRING('1_4_4' from 3 for 1), clusterZ = SUBSTRING('1_4_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N036' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_9' from 1 for 1), clusterY = SUBSTRING('2_5_9' from 3 for 1), clusterZ = SUBSTRING('2_5_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N037' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N040' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N048' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_1' from 1 for 1), clusterY = SUBSTRING('2_4_1' from 3 for 1), clusterZ = SUBSTRING('2_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N049' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_5' from 1 for 1), clusterY = SUBSTRING('1_4_5' from 3 for 1), clusterZ = SUBSTRING('1_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N050' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N054' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_7' from 1 for 1), clusterY = SUBSTRING('5_4_7' from 3 for 1), clusterZ = SUBSTRING('5_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N055' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_2' from 1 for 1), clusterY = SUBSTRING('1_4_2' from 3 for 1), clusterZ = SUBSTRING('1_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N057' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_7' from 1 for 1), clusterY = SUBSTRING('1_2_7' from 3 for 1), clusterZ = SUBSTRING('1_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N058' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N061' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_4' from 1 for 1), clusterY = SUBSTRING('3_4_4' from 3 for 1), clusterZ = SUBSTRING('3_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N064' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_3' from 1 for 1), clusterY = SUBSTRING('4_1_3' from 3 for 1), clusterZ = SUBSTRING('4_1_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N065' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_9' from 1 for 1), clusterY = SUBSTRING('3_4_9' from 3 for 1), clusterZ = SUBSTRING('3_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N068' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_8' from 1 for 1), clusterY = SUBSTRING('2_5_8' from 3 for 1), clusterZ = SUBSTRING('2_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N070' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_5' from 1 for 1), clusterY = SUBSTRING('1_2_5' from 3 for 1), clusterZ = SUBSTRING('1_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N079' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_6' from 1 for 1), clusterY = SUBSTRING('5_5_6' from 3 for 1), clusterZ = SUBSTRING('5_5_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N085' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_5' from 1 for 1), clusterY = SUBSTRING('2_2_5' from 3 for 1), clusterZ = SUBSTRING('2_2_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N086' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_9' from 1 for 1), clusterY = SUBSTRING('1_2_9' from 3 for 1), clusterZ = SUBSTRING('1_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N089' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N092' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_5' from 1 for 1), clusterY = SUBSTRING('2_4_5' from 3 for 1), clusterZ = SUBSTRING('2_4_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N093' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_10' from 1 for 1), clusterY = SUBSTRING('3_5_10' from 3 for 1), clusterZ = SUBSTRING('3_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N098' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N101' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N104' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_6' from 1 for 1), clusterY = SUBSTRING('1_3_6' from 3 for 1), clusterZ = SUBSTRING('1_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N105' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_10' from 1 for 1), clusterY = SUBSTRING('4_1_10' from 3 for 1), clusterZ = SUBSTRING('4_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N106' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_1' from 1 for 1), clusterY = SUBSTRING('3_4_1' from 3 for 1), clusterZ = SUBSTRING('3_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N109' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_10' from 1 for 1), clusterY = SUBSTRING('2_5_10' from 3 for 1), clusterZ = SUBSTRING('2_5_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N116' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_1' from 1 for 1), clusterY = SUBSTRING('5_4_1' from 3 for 1), clusterZ = SUBSTRING('5_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N120' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_9' from 1 for 1), clusterY = SUBSTRING('3_3_9' from 3 for 1), clusterZ = SUBSTRING('3_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N124' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_3' from 1 for 1), clusterY = SUBSTRING('4_5_3' from 3 for 1), clusterZ = SUBSTRING('4_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N127' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N130' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N131' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_4' from 1 for 1), clusterY = SUBSTRING('4_3_4' from 3 for 1), clusterZ = SUBSTRING('4_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N138' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N142' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_4' from 1 for 1), clusterY = SUBSTRING('3_4_4' from 3 for 1), clusterZ = SUBSTRING('3_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N143' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N144' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N145' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N148' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_9' from 1 for 1), clusterY = SUBSTRING('3_4_9' from 3 for 1), clusterZ = SUBSTRING('3_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N152' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_8' from 1 for 1), clusterY = SUBSTRING('5_5_8' from 3 for 1), clusterZ = SUBSTRING('5_5_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N156' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_4' from 1 for 1), clusterY = SUBSTRING('5_5_4' from 3 for 1), clusterZ = SUBSTRING('5_5_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N161' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N165' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_9' from 1 for 1), clusterY = SUBSTRING('4_3_9' from 3 for 1), clusterZ = SUBSTRING('4_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N167' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_4' from 1 for 1), clusterY = SUBSTRING('2_3_4' from 3 for 1), clusterZ = SUBSTRING('2_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N172' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_10' from 1 for 1), clusterY = SUBSTRING('1_2_10' from 3 for 1), clusterZ = SUBSTRING('1_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N173' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N177' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N178' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_10' from 1 for 1), clusterY = SUBSTRING('1_2_10' from 3 for 1), clusterZ = SUBSTRING('1_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N180' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_9' from 1 for 1), clusterY = SUBSTRING('2_3_9' from 3 for 1), clusterZ = SUBSTRING('2_3_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N187' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_7' from 1 for 1), clusterY = SUBSTRING('1_1_7' from 3 for 1), clusterZ = SUBSTRING('1_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N188' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_2' from 1 for 1), clusterY = SUBSTRING('2_2_2' from 3 for 1), clusterZ = SUBSTRING('2_2_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N189' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_8' from 1 for 1), clusterY = SUBSTRING('1_3_8' from 3 for 1), clusterZ = SUBSTRING('1_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N191' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N194' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_9' from 1 for 1), clusterY = SUBSTRING('2_3_9' from 3 for 1), clusterZ = SUBSTRING('2_3_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N195' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_9' from 1 for 1), clusterY = SUBSTRING('1_3_9' from 3 for 1), clusterZ = SUBSTRING('1_3_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N211' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_8' from 1 for 1), clusterY = SUBSTRING('2_4_8' from 3 for 1), clusterZ = SUBSTRING('2_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N212' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_9' from 1 for 1), clusterY = SUBSTRING('1_5_9' from 3 for 1), clusterZ = SUBSTRING('1_5_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N214' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_7' from 1 for 1), clusterY = SUBSTRING('3_4_7' from 3 for 1), clusterZ = SUBSTRING('3_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N216' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_6' from 1 for 1), clusterY = SUBSTRING('1_4_6' from 3 for 1), clusterZ = SUBSTRING('1_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N217' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_5' from 1 for 1), clusterY = SUBSTRING('3_4_5' from 3 for 1), clusterZ = SUBSTRING('3_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N218' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_7' from 1 for 1), clusterY = SUBSTRING('5_2_7' from 3 for 1), clusterZ = SUBSTRING('5_2_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N222' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N225' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N229' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_6' from 1 for 1), clusterY = SUBSTRING('2_1_6' from 3 for 1), clusterZ = SUBSTRING('2_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N242' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_7' from 1 for 1), clusterY = SUBSTRING('5_4_7' from 3 for 1), clusterZ = SUBSTRING('5_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N247' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_5' from 1 for 1), clusterY = SUBSTRING('3_3_5' from 3 for 1), clusterZ = SUBSTRING('3_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N248' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N252' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_7' from 1 for 1), clusterY = SUBSTRING('3_4_7' from 3 for 1), clusterZ = SUBSTRING('3_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N255' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_3' from 1 for 1), clusterY = SUBSTRING('2_1_3' from 3 for 1), clusterZ = SUBSTRING('2_1_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N265' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_8' from 1 for 1), clusterY = SUBSTRING('5_3_8' from 3 for 1), clusterZ = SUBSTRING('5_3_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N267' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N268' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N270' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_5' from 1 for 1), clusterY = SUBSTRING('5_2_5' from 3 for 1), clusterZ = SUBSTRING('5_2_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N283' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N284' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_9' from 1 for 1), clusterY = SUBSTRING('3_4_9' from 3 for 1), clusterZ = SUBSTRING('3_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N286' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_10' from 1 for 1), clusterY = SUBSTRING('2_2_10' from 3 for 1), clusterZ = SUBSTRING('2_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N288' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_3' from 1 for 1), clusterY = SUBSTRING('4_4_3' from 3 for 1), clusterZ = SUBSTRING('4_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N294' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_5_7' from 1 for 1), clusterY = SUBSTRING('1_5_7' from 3 for 1), clusterZ = SUBSTRING('1_5_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N298' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_6' from 1 for 1), clusterY = SUBSTRING('5_3_6' from 3 for 1), clusterZ = SUBSTRING('5_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N301' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_10' from 1 for 1), clusterY = SUBSTRING('4_3_10' from 3 for 1), clusterZ = SUBSTRING('4_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N307' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_8' from 1 for 1), clusterY = SUBSTRING('1_2_8' from 3 for 1), clusterZ = SUBSTRING('1_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N312' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_10' from 1 for 1), clusterY = SUBSTRING('3_1_10' from 3 for 1), clusterZ = SUBSTRING('3_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N313' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N315' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_4' from 1 for 1), clusterY = SUBSTRING('4_3_4' from 3 for 1), clusterZ = SUBSTRING('4_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N316' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_7' from 1 for 1), clusterY = SUBSTRING('3_4_7' from 3 for 1), clusterZ = SUBSTRING('3_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N317' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_6' from 1 for 1), clusterY = SUBSTRING('4_3_6' from 3 for 1), clusterZ = SUBSTRING('4_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N319' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_2' from 1 for 1), clusterY = SUBSTRING('1_1_2' from 3 for 1), clusterZ = SUBSTRING('1_1_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N320' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_5' from 1 for 1), clusterY = SUBSTRING('4_5_5' from 3 for 1), clusterZ = SUBSTRING('4_5_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N321' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_1' from 1 for 1), clusterY = SUBSTRING('4_5_1' from 3 for 1), clusterZ = SUBSTRING('4_5_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N323' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_10' from 1 for 1), clusterY = SUBSTRING('3_4_10' from 3 for 1), clusterZ = SUBSTRING('3_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N328' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_2' from 1 for 1), clusterY = SUBSTRING('2_4_2' from 3 for 1), clusterZ = SUBSTRING('2_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N329' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_7' from 1 for 1), clusterY = SUBSTRING('3_1_7' from 3 for 1), clusterZ = SUBSTRING('3_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N335' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_4' from 1 for 1), clusterY = SUBSTRING('2_5_4' from 3 for 1), clusterZ = SUBSTRING('2_5_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N341' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_8' from 1 for 1), clusterY = SUBSTRING('3_2_8' from 3 for 1), clusterZ = SUBSTRING('3_2_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N344' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_10' from 1 for 1), clusterY = SUBSTRING('3_3_10' from 3 for 1), clusterZ = SUBSTRING('3_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N345' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_2' from 1 for 1), clusterY = SUBSTRING('2_3_2' from 3 for 1), clusterZ = SUBSTRING('2_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N346' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_4' from 1 for 1), clusterY = SUBSTRING('4_1_4' from 3 for 1), clusterZ = SUBSTRING('4_1_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N351' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_10' from 1 for 1), clusterY = SUBSTRING('5_3_10' from 3 for 1), clusterZ = SUBSTRING('5_3_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N352' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_7' from 1 for 1), clusterY = SUBSTRING('3_2_7' from 3 for 1), clusterZ = SUBSTRING('3_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N359' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N363' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_8' from 1 for 1), clusterY = SUBSTRING('3_3_8' from 3 for 1), clusterZ = SUBSTRING('3_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N365' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N367' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_2' from 1 for 1), clusterY = SUBSTRING('3_4_2' from 3 for 1), clusterZ = SUBSTRING('3_4_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N369' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_4' from 1 for 1), clusterY = SUBSTRING('4_1_4' from 3 for 1), clusterZ = SUBSTRING('4_1_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N371' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N384' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N388' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_3' from 1 for 1), clusterY = SUBSTRING('4_5_3' from 3 for 1), clusterZ = SUBSTRING('4_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N391' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N392' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_6' from 1 for 1), clusterY = SUBSTRING('2_2_6' from 3 for 1), clusterZ = SUBSTRING('2_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N400' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N416' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_8' from 1 for 1), clusterY = SUBSTRING('3_3_8' from 3 for 1), clusterZ = SUBSTRING('3_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N424' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_3' from 1 for 1), clusterY = SUBSTRING('4_2_3' from 3 for 1), clusterZ = SUBSTRING('4_2_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N428' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_8' from 1 for 1), clusterY = SUBSTRING('5_4_8' from 3 for 1), clusterZ = SUBSTRING('5_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N429' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_2' from 1 for 1), clusterY = SUBSTRING('2_3_2' from 3 for 1), clusterZ = SUBSTRING('2_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N430' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N437' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_8' from 1 for 1), clusterY = SUBSTRING('4_2_8' from 3 for 1), clusterZ = SUBSTRING('4_2_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N439' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_3' from 1 for 1), clusterY = SUBSTRING('4_1_3' from 3 for 1), clusterZ = SUBSTRING('4_1_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N440' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_5' from 1 for 1), clusterY = SUBSTRING('1_3_5' from 3 for 1), clusterZ = SUBSTRING('1_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N442' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_7' from 1 for 1), clusterY = SUBSTRING('4_4_7' from 3 for 1), clusterZ = SUBSTRING('4_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N444' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_10' from 1 for 1), clusterY = SUBSTRING('1_4_10' from 3 for 1), clusterZ = SUBSTRING('1_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N445' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_4' from 1 for 1), clusterY = SUBSTRING('5_5_4' from 3 for 1), clusterZ = SUBSTRING('5_5_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N446' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N451' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_1' from 1 for 1), clusterY = SUBSTRING('2_2_1' from 3 for 1), clusterZ = SUBSTRING('2_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N458' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_6' from 1 for 1), clusterY = SUBSTRING('2_3_6' from 3 for 1), clusterZ = SUBSTRING('2_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N459' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_8' from 1 for 1), clusterY = SUBSTRING('1_1_8' from 3 for 1), clusterZ = SUBSTRING('1_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N465' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_6' from 1 for 1), clusterY = SUBSTRING('5_3_6' from 3 for 1), clusterZ = SUBSTRING('5_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N466' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_6' from 1 for 1), clusterY = SUBSTRING('1_1_6' from 3 for 1), clusterZ = SUBSTRING('1_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N478' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N479' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_3' from 1 for 1), clusterY = SUBSTRING('4_3_3' from 3 for 1), clusterZ = SUBSTRING('4_3_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N480' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N487' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N489' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_10' from 1 for 1), clusterY = SUBSTRING('2_4_10' from 3 for 1), clusterZ = SUBSTRING('2_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N494' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_1' from 1 for 1), clusterY = SUBSTRING('3_4_1' from 3 for 1), clusterZ = SUBSTRING('3_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N495' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_2' from 1 for 1), clusterY = SUBSTRING('5_2_2' from 3 for 1), clusterZ = SUBSTRING('5_2_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N506' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N525' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_8' from 1 for 1), clusterY = SUBSTRING('5_4_8' from 3 for 1), clusterZ = SUBSTRING('5_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N526' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_8' from 1 for 1), clusterY = SUBSTRING('3_4_8' from 3 for 1), clusterZ = SUBSTRING('3_4_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N527' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N529' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_5' from 1 for 1), clusterY = SUBSTRING('1_1_5' from 3 for 1), clusterZ = SUBSTRING('1_1_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N532' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_6' from 1 for 1), clusterY = SUBSTRING('3_4_6' from 3 for 1), clusterZ = SUBSTRING('3_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N533' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_6' from 1 for 1), clusterY = SUBSTRING('4_4_6' from 3 for 1), clusterZ = SUBSTRING('4_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N536' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_4' from 1 for 1), clusterY = SUBSTRING('3_3_4' from 3 for 1), clusterZ = SUBSTRING('3_3_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N537' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_10' from 1 for 1), clusterY = SUBSTRING('5_1_10' from 3 for 1), clusterZ = SUBSTRING('5_1_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N539' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N543' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N547' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_1' from 1 for 1), clusterY = SUBSTRING('5_1_1' from 3 for 1), clusterZ = SUBSTRING('5_1_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N554' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_1' from 1 for 1), clusterY = SUBSTRING('4_3_1' from 3 for 1), clusterZ = SUBSTRING('4_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N556' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_10' from 1 for 1), clusterY = SUBSTRING('2_3_10' from 3 for 1), clusterZ = SUBSTRING('2_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N567' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_8' from 1 for 1), clusterY = SUBSTRING('3_5_8' from 3 for 1), clusterZ = SUBSTRING('3_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N569' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_2' from 1 for 1), clusterY = SUBSTRING('4_4_2' from 3 for 1), clusterZ = SUBSTRING('4_4_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N574' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_8' from 1 for 1), clusterY = SUBSTRING('4_5_8' from 3 for 1), clusterZ = SUBSTRING('4_5_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N584' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_3' from 1 for 1), clusterY = SUBSTRING('1_2_3' from 3 for 1), clusterZ = SUBSTRING('1_2_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N585' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_6' from 1 for 1), clusterY = SUBSTRING('2_3_6' from 3 for 1), clusterZ = SUBSTRING('2_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N590' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_9' from 1 for 1), clusterY = SUBSTRING('5_4_9' from 3 for 1), clusterZ = SUBSTRING('5_4_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N591' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_8' from 1 for 1), clusterY = SUBSTRING('3_5_8' from 3 for 1), clusterZ = SUBSTRING('3_5_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N645' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_4' from 1 for 1), clusterY = SUBSTRING('4_2_4' from 3 for 1), clusterZ = SUBSTRING('4_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N680' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_3' from 1 for 1), clusterY = SUBSTRING('4_5_3' from 3 for 1), clusterZ = SUBSTRING('4_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N698' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_7' from 1 for 1), clusterY = SUBSTRING('4_3_7' from 3 for 1), clusterZ = SUBSTRING('4_3_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N713' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_7' from 1 for 1), clusterY = SUBSTRING('3_2_7' from 3 for 1), clusterZ = SUBSTRING('3_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N724' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_4' from 1 for 1), clusterY = SUBSTRING('4_4_4' from 3 for 1), clusterZ = SUBSTRING('4_4_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N729' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_9' from 1 for 1), clusterY = SUBSTRING('1_1_9' from 3 for 1), clusterZ = SUBSTRING('1_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N734' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_8' from 1 for 1), clusterY = SUBSTRING('2_3_8' from 3 for 1), clusterZ = SUBSTRING('2_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N843' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_5' from 1 for 1), clusterY = SUBSTRING('2_3_5' from 3 for 1), clusterZ = SUBSTRING('2_3_5' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N845' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N846' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_1' from 1 for 1), clusterY = SUBSTRING('1_4_1' from 3 for 1), clusterZ = SUBSTRING('1_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N852' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_9' from 1 for 1), clusterY = SUBSTRING('2_1_9' from 3 for 1), clusterZ = SUBSTRING('2_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N863' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_7' from 1 for 1), clusterY = SUBSTRING('3_1_7' from 3 for 1), clusterZ = SUBSTRING('3_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N872' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_1' from 1 for 1), clusterY = SUBSTRING('1_3_1' from 3 for 1), clusterZ = SUBSTRING('1_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N882' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_10' from 1 for 1), clusterY = SUBSTRING('3_5_10' from 3 for 1), clusterZ = SUBSTRING('3_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N884' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_3' from 1 for 1), clusterY = SUBSTRING('5_4_3' from 3 for 1), clusterZ = SUBSTRING('5_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N886' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_10' from 1 for 1), clusterY = SUBSTRING('3_2_10' from 3 for 1), clusterZ = SUBSTRING('3_2_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N894' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_3' from 1 for 1), clusterY = SUBSTRING('1_4_3' from 3 for 1), clusterZ = SUBSTRING('1_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N895' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N904' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_1' from 1 for 1), clusterY = SUBSTRING('4_2_1' from 3 for 1), clusterZ = SUBSTRING('4_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N914' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_1' from 1 for 1), clusterY = SUBSTRING('5_4_1' from 3 for 1), clusterZ = SUBSTRING('5_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N919' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_10' from 1 for 1), clusterY = SUBSTRING('5_4_10' from 3 for 1), clusterZ = SUBSTRING('5_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N920' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_7' from 1 for 1), clusterY = SUBSTRING('2_2_7' from 3 for 1), clusterZ = SUBSTRING('2_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N921' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_3' from 1 for 1), clusterY = SUBSTRING('3_3_3' from 3 for 1), clusterZ = SUBSTRING('3_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N929' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N930' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_4' from 1 for 1), clusterY = SUBSTRING('3_2_4' from 3 for 1), clusterZ = SUBSTRING('3_2_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N932' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_8' from 1 for 1), clusterY = SUBSTRING('1_3_8' from 3 for 1), clusterZ = SUBSTRING('1_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N938' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_9' from 1 for 1), clusterY = SUBSTRING('5_2_9' from 3 for 1), clusterZ = SUBSTRING('5_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N940' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_5' from 1 for 1), clusterY = SUBSTRING('4_3_5' from 3 for 1), clusterZ = SUBSTRING('4_3_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N941' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_8' from 1 for 1), clusterY = SUBSTRING('1_3_8' from 3 for 1), clusterZ = SUBSTRING('1_3_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N948' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_10' from 1 for 1), clusterY = SUBSTRING('1_3_10' from 3 for 1), clusterZ = SUBSTRING('1_3_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N956' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_6' from 1 for 1), clusterY = SUBSTRING('2_4_6' from 3 for 1), clusterZ = SUBSTRING('2_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N957' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_10' from 1 for 1), clusterY = SUBSTRING('2_5_10' from 3 for 1), clusterZ = SUBSTRING('2_5_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N958' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_6' from 1 for 1), clusterY = SUBSTRING('3_1_6' from 3 for 1), clusterZ = SUBSTRING('3_1_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N959' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_5' from 1 for 1), clusterY = SUBSTRING('4_1_5' from 3 for 1), clusterZ = SUBSTRING('4_1_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N972' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_6' from 1 for 1), clusterY = SUBSTRING('3_3_6' from 3 for 1), clusterZ = SUBSTRING('3_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N974' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_5' from 1 for 1), clusterY = SUBSTRING('5_4_5' from 3 for 1), clusterZ = SUBSTRING('5_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N976' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_3' from 1 for 1), clusterY = SUBSTRING('3_2_3' from 3 for 1), clusterZ = SUBSTRING('3_2_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N977' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_10' from 1 for 1), clusterY = SUBSTRING('1_1_10' from 3 for 1), clusterZ = SUBSTRING('1_1_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N982' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_7' from 1 for 1), clusterY = SUBSTRING('2_4_7' from 3 for 1), clusterZ = SUBSTRING('2_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N985' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_5_6' from 1 for 1), clusterY = SUBSTRING('4_5_6' from 3 for 1), clusterZ = SUBSTRING('4_5_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N989' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_1' from 1 for 1), clusterY = SUBSTRING('3_3_1' from 3 for 1), clusterZ = SUBSTRING('3_3_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N991' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_10' from 1 for 1), clusterY = SUBSTRING('1_2_10' from 3 for 1), clusterZ = SUBSTRING('1_2_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N992' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_7' from 1 for 1), clusterY = SUBSTRING('3_4_7' from 3 for 1), clusterZ = SUBSTRING('3_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'N993' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_6' from 1 for 1), clusterY = SUBSTRING('4_3_6' from 3 for 1), clusterZ = SUBSTRING('4_3_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NA54' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_1_5' from 1 for 1), clusterY = SUBSTRING('5_1_5' from 3 for 1), clusterZ = SUBSTRING('5_1_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NA55' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_1' from 1 for 1), clusterY = SUBSTRING('5_2_1' from 3 for 1), clusterZ = SUBSTRING('5_2_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NA92' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_9' from 1 for 1), clusterY = SUBSTRING('5_3_9' from 3 for 1), clusterZ = SUBSTRING('5_3_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NA97' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB07' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB17' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_4' from 1 for 1), clusterY = SUBSTRING('1_3_4' from 3 for 1), clusterZ = SUBSTRING('1_3_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB36' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_7' from 1 for 1), clusterY = SUBSTRING('1_2_7' from 3 for 1), clusterZ = SUBSTRING('1_2_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB39' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_6' from 1 for 1), clusterY = SUBSTRING('3_3_6' from 3 for 1), clusterZ = SUBSTRING('3_3_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB42' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_6' from 1 for 1), clusterY = SUBSTRING('2_4_6' from 3 for 1), clusterZ = SUBSTRING('2_4_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB43' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_9' from 1 for 1), clusterY = SUBSTRING('4_2_9' from 3 for 1), clusterZ = SUBSTRING('4_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB44' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_2' from 1 for 1), clusterY = SUBSTRING('1_3_2' from 3 for 1), clusterZ = SUBSTRING('1_3_2' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NB63' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_1' from 1 for 1), clusterY = SUBSTRING('5_4_1' from 3 for 1), clusterZ = SUBSTRING('5_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC04' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_5_4' from 1 for 1), clusterY = SUBSTRING('3_5_4' from 3 for 1), clusterZ = SUBSTRING('3_5_4' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC07' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC09' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_6' from 1 for 1), clusterY = SUBSTRING('5_4_6' from 3 for 1), clusterZ = SUBSTRING('5_4_6' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC11' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_8' from 1 for 1), clusterY = SUBSTRING('3_1_8' from 3 for 1), clusterZ = SUBSTRING('3_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC16' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_5' from 1 for 1), clusterY = SUBSTRING('5_4_5' from 3 for 1), clusterZ = SUBSTRING('5_4_5' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC27' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_10' from 1 for 1), clusterY = SUBSTRING('5_5_10' from 3 for 1), clusterZ = SUBSTRING('5_5_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC29' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_10' from 1 for 1), clusterY = SUBSTRING('3_4_10' from 3 for 1), clusterZ = SUBSTRING('3_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC34' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_2_9' from 1 for 1), clusterY = SUBSTRING('5_2_9' from 3 for 1), clusterZ = SUBSTRING('5_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC36' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_4' from 1 for 1), clusterY = SUBSTRING('1_2_4' from 3 for 1), clusterZ = SUBSTRING('1_2_4' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC40' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_7' from 1 for 1), clusterY = SUBSTRING('5_4_7' from 3 for 1), clusterZ = SUBSTRING('5_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC46' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_2' from 1 for 1), clusterY = SUBSTRING('4_1_2' from 3 for 1), clusterZ = SUBSTRING('4_1_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC53' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC55' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_1' from 1 for 1), clusterY = SUBSTRING('1_4_1' from 3 for 1), clusterZ = SUBSTRING('1_4_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC60' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_1' from 1 for 1), clusterY = SUBSTRING('4_4_1' from 3 for 1), clusterZ = SUBSTRING('4_4_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC70' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_7' from 1 for 1), clusterY = SUBSTRING('5_4_7' from 3 for 1), clusterZ = SUBSTRING('5_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC73' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC80' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_4_8' from 1 for 1), clusterY = SUBSTRING('5_4_8' from 3 for 1), clusterZ = SUBSTRING('5_4_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC82' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_2_6' from 1 for 1), clusterY = SUBSTRING('1_2_6' from 3 for 1), clusterZ = SUBSTRING('1_2_6' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC86' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_5_1' from 1 for 1), clusterY = SUBSTRING('2_5_1' from 3 for 1), clusterZ = SUBSTRING('2_5_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC90' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_3' from 1 for 1), clusterY = SUBSTRING('4_4_3' from 3 for 1), clusterZ = SUBSTRING('4_4_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC91' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_3' from 1 for 1), clusterY = SUBSTRING('2_4_3' from 3 for 1), clusterZ = SUBSTRING('2_4_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NC97' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_2' from 1 for 1), clusterY = SUBSTRING('4_3_2' from 3 for 1), clusterZ = SUBSTRING('4_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND00' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_7' from 1 for 1), clusterY = SUBSTRING('1_1_7' from 3 for 1), clusterZ = SUBSTRING('1_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND03' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_9' from 1 for 1), clusterY = SUBSTRING('2_4_9' from 3 for 1), clusterZ = SUBSTRING('2_4_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND06' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_9' from 1 for 1), clusterY = SUBSTRING('1_1_9' from 3 for 1), clusterZ = SUBSTRING('1_1_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND16' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_7' from 1 for 1), clusterY = SUBSTRING('4_4_7' from 3 for 1), clusterZ = SUBSTRING('4_4_7' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND17' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_3_8' from 1 for 1), clusterY = SUBSTRING('4_3_8' from 3 for 1), clusterZ = SUBSTRING('4_3_8' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND23' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_2_2' from 1 for 1), clusterY = SUBSTRING('4_2_2' from 3 for 1), clusterZ = SUBSTRING('4_2_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND29' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_1_3' from 1 for 1), clusterY = SUBSTRING('4_1_3' from 3 for 1), clusterZ = SUBSTRING('4_1_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND34' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_1_9' from 1 for 1), clusterY = SUBSTRING('3_1_9' from 3 for 1), clusterZ = SUBSTRING('3_1_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND47' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND54' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_3' from 1 for 1), clusterY = SUBSTRING('3_3_3' from 3 for 1), clusterZ = SUBSTRING('3_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND55' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('4_4_10' from 1 for 1), clusterY = SUBSTRING('4_4_10' from 3 for 1), clusterZ = SUBSTRING('4_4_10' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND59' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_1' from 1 for 1), clusterY = SUBSTRING('2_1_1' from 3 for 1), clusterZ = SUBSTRING('2_1_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND62' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_7' from 1 for 1), clusterY = SUBSTRING('1_3_7' from 3 for 1), clusterZ = SUBSTRING('1_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND66' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_3_3' from 1 for 1), clusterY = SUBSTRING('1_3_3' from 3 for 1), clusterZ = SUBSTRING('1_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND70' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_9' from 1 for 1), clusterY = SUBSTRING('2_2_9' from 3 for 1), clusterZ = SUBSTRING('2_2_9' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND75' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_2_9' from 1 for 1), clusterY = SUBSTRING('3_2_9' from 3 for 1), clusterZ = SUBSTRING('3_2_9' from 5),
                          cluster_id = case 'Fragili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND78' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_1' from 1 for 1), clusterY = SUBSTRING('5_3_1' from 3 for 1), clusterZ = SUBSTRING('5_3_1' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND81' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_4_10' from 1 for 1), clusterY = SUBSTRING('2_4_10' from 3 for 1), clusterZ = SUBSTRING('2_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND93' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_3_7' from 1 for 1), clusterY = SUBSTRING('3_3_7' from 3 for 1), clusterZ = SUBSTRING('3_3_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'ND97' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_2' from 1 for 1), clusterY = SUBSTRING('5_5_2' from 3 for 1), clusterZ = SUBSTRING('5_5_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF03' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_1_8' from 1 for 1), clusterY = SUBSTRING('1_1_8' from 3 for 1), clusterZ = SUBSTRING('1_1_8' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF10' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_2_1' from 1 for 1), clusterY = SUBSTRING('2_2_1' from 3 for 1), clusterZ = SUBSTRING('2_2_1' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF22' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('1_4_10' from 1 for 1), clusterY = SUBSTRING('1_4_10' from 3 for 1), clusterZ = SUBSTRING('1_4_10' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF29' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_1_7' from 1 for 1), clusterY = SUBSTRING('2_1_7' from 3 for 1), clusterZ = SUBSTRING('2_1_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF40' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('2_3_3' from 1 for 1), clusterY = SUBSTRING('2_3_3' from 3 for 1), clusterZ = SUBSTRING('2_3_3' from 5),
                          cluster_id = case 'Strutturate' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF49' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('3_4_7' from 1 for 1), clusterY = SUBSTRING('3_4_7' from 3 for 1), clusterZ = SUBSTRING('3_4_7' from 5),
                          cluster_id = case 'Sanabili' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF54' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_3_2' from 1 for 1), clusterY = SUBSTRING('5_3_2' from 3 for 1), clusterZ = SUBSTRING('5_3_2' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF61' AND anno = 2022;
update pianiag2_piani set clusterX = SUBSTRING('5_5_3' from 1 for 1), clusterY = SUBSTRING('5_5_3' from 3 for 1), clusterZ = SUBSTRING('5_5_3' from 5),
                          cluster_id = case 'Astri' when 'Strutturate' then 1 when 'Astri' then 2 when 'Sanabili' then 3 when 'Fragili' then 4 end where agenzia_id = 'NF67' AND anno = 2022;