/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, startDate, endDate) VALUES ('152', 'Casa', 'Casa', 'OFF', 'OFF', 'OFF', '2016-10-20', '2016-12-31');
-- ALTER TABLE stats_users_access_daily  ADD I152 SMALLINT UNSIGNED NOT NULL DEFAULT 0 AFTER I151;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_152;
CREATE TABLE IF NOT EXISTS iniz_152 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						TINYINT(1) UNSIGNED not NULL DEFAULT 0,
	obiettivo					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	pezziQACconHB				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziQACnoHB				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiQACnoHB				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiQACconHB				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompQACnoHB			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompQACconHB			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz152 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_152;
CREATE VIEW vw_iniz_152 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.pezziQACconHB,
	st.pezziQACnoHB,
	st.pezziTOTALI,
	st.premiQACnoHB,
	st.premiQACconHB,
	st.premiTOTALI,
	st.premiCompQACnoHB,
	st.premiCompQACconHB,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_152 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_152_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_152_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_152 (
		agenzia_id, objGAshow, objGAobjPremi, gruppo, obiettivo
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_gruppo, p_objInizPezzi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, gruppo = p_gruppo, obiettivo = p_objInizPezzi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_152_set_status;
DELIMITER //
CREATE PROCEDURE iniz_152_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 152;
	DECLARE done 							INT DEFAULT 0;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_gruppo						TINYINT UNSIGNED;
	DECLARE v_obiettivo						TINYINT UNSIGNED;

	DECLARE v_pezziQACconHB					SMALLINT UNSIGNED;
	DECLARE v_pezziQACnoHB					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiQACnoHB					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiQACconHB					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompQACnoHB				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompQACconHB				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, gruppo, obiettivo, objGAobjPremi FROM iniz_152 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 152;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_gruppo, v_obiettivo, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_pezziQACconHB = 0; SET v_pezziQACnoHB = 0; SET v_pezziTOTALI = 0;
		SET v_premiQACnoHB = 0; SET v_premiQACconHB = 0; SET v_premiTOTALI = 0;
		SET v_premiCompQACnoHB = 0; SET v_premiCompQACconHB = 0; SET v_premiCompTOTALI = 0;
		SET v_obiettivoOK = 0;
		SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- QuiAbitoCasa con HomeBox
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiQACconHB, v_premiCompQACconHB
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto IN('000047', '000056');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziQACconHB
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto IN('000047', '000056');

		-- QuiAbitoCasa senza HomeBox
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiQACnoHB, v_premiCompQACnoHB
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto NOT IN('000047', '000056');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziQACnoHB
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto NOT IN('000047', '000056');

		-- totali & status
		SET v_pezziTOTALI = v_pezziQACconHB + v_pezziQACnoHB;
		SET v_premiTOTALI = v_premiQACnoHB + v_premiQACconHB;
		SET v_premiCompTOTALI = v_premiCompQACnoHB + v_premiCompQACconHB;
		IF v_pezziQACconHB >= v_obiettivo THEN
			SET v_obiettivoOK = 1;
			SET v_importoErogTOTALE = v_obiettivo * 15 + (v_pezziTOTALI - v_obiettivo) * 20;
		END IF;

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_152 SET
			pezziQACnoHB = v_pezziQACnoHB, pezziQACconHB = v_pezziQACconHB, pezziTOTALI = v_pezziTOTALI,
			premiQACnoHB = v_premiQACnoHB, premiQACconHB = v_premiQACconHB, premiTOTALI = v_premiTOTALI,
			premiCompQACnoHB = v_premiCompQACnoHB, premiCompQACconHB = v_premiCompQACconHB, premiCompTOTALI = v_premiCompTOTALI,
			obiettivoOK = v_obiettivoOK,
			importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_gruppo, v_obiettivo, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
