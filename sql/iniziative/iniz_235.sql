/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('235', 'Plus Building', 'PlusBuilding', 'OFF', 'OFF', 'OFF', '2020-10-01', '2020-12-31');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_235;
CREATE TABLE IF NOT EXISTS iniz_235 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						CHAR(1) NOT NULL,
	ptfInizio					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	ptfFine						DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPezzi				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	premiOver170				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziOver170				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	statusPtfOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz235 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_235;
CREATE VIEW vw_iniz_235 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.ptfInizio,
	st.ptfFine,
	st.obiettivoPezzi,
	st.premiOver170,
	st.premiTOTALI,
	st.pezziOver170,
	st.pezziTOTALI,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.statusPtfOK,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_235 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_235_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_235_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),
	IN p_objInizPremi	DECIMAL(12,2),
	IN p_objInizPezzi	MEDIUMINT,
	IN p_ptf			DECIMAL(12,2)
)
BEGIN
	INSERT INTO iniz_235 (
		agenzia_id, objGAshow, objGAobjPremi, gruppo, ptfInizio, ptfFine, obiettivoPezzi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_gruppo, p_ptf, p_objInizPremi, p_objInizPezzi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, gruppo = p_gruppo, ptfInizio = p_ptf, ptfFine = p_objInizPremi, obiettivoPezzi = p_objInizPezzi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_235_set_status;
DELIMITER //
CREATE PROCEDURE iniz_235_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 235;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_DATI_FINALI					TINYINT UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;
	DECLARE v_ptfInizio						DECIMAL(12,2) UNSIGNED;
	DECLARE v_ptfFine						DECIMAL(12,2) UNSIGNED;
	DECLARE v_obiettivoPezzi				TINYINT UNSIGNED;

	DECLARE v_premiOver170					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziOver170					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;

	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_statusPtfOK					TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi, ptfInizio, ptfFine, obiettivoPezzi  FROM iniz_235 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 235;

	-- get switch bonus ON/OFF
	SELECT datiFinali INTO C_DATI_FINALI FROM iniz WHERE id = C_INIZIATIVA_ID;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi, v_ptfInizio, v_ptfFine, v_obiettivoPezzi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiOver170 = 0; SET v_premiTOTALI = 0;
		SET v_pezziOver170 = 0; SET v_pezziTOTALI = 0;
		SET v_premiCompTOTALI = 0;
		SET v_obiettivoOK = 0; SET v_statusPtfOK = 0;
		SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiTOTALI, v_premiCompTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND motivo = 'N';
		SELECT IFNULL(SUM(premioComputabile),0) INTO v_premiOver170
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND motivo = 'N' AND premioComputabile >= 170;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziOver170
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND motivo = 'N' AND premioComputabile >= 170;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND motivo = 'N';

		-- incentivazione
		IF v_pezziOver170 >= v_obiettivoPezzi THEN
			SET v_obiettivoOK = 1;
			SET v_importoErogTOTALE = v_premiOver170 * 0.10;

		END IF;
		-- status PFT
		IF v_ptfFine >= v_ptfInizio THEN
			SET v_statusPtfOK = 1;
		ELSE
			SET v_statusPtfOK = 0;
			SET v_importoErogTOTALE = 0;
		END IF;


		-- PROGRESS BAR
		SET v_progress = LEAST(100, v_pezziOver170 / v_obiettivoPezzi * 100);
		SET v_progress2 = LEAST(100, v_ptfFine / v_ptfInizio * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, NULL);

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_235 SET
			premiOver170 = v_premiOver170, premiTOTALI = v_premiTOTALI,
			pezziOver170 = v_pezziOver170, pezziTOTALI = v_pezziTOTALI,
			premiCompTOTALI = v_premiCompTOTALI,
			obiettivoOK = v_obiettivoOK, statusPtfOK = v_statusPtfOK,
			importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi, v_ptfInizio, v_ptfFine, v_obiettivoPezzi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
