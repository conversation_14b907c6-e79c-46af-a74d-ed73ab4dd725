@font-face {
    font-family: 'Icoagendo';
    src:  url('Icoagendo.eot?oav7et');
    src:  url('Icoagendo.eot?oav7et#iefix') format('embedded-opentype'),
    url('Icoagendo.ttf?oav7et') format('truetype'),
    url('Icoagendo.woff?oav7et') format('woff'),
    url('Icoagendo.svg?oav7et#Icoagendo') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icoagendo-"], [class*=" icoagendo-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'Icoagendo' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


/* Menu */
.icoagendo-menu-incentivazioni:before {
    content: "\e900";
}
.icoagendo-menu-news:before {
    content: "\e904";
}
.icoagendo-menu-formazione:before {
    content: "\e91f";
}
.icoagendo-menu-helpdesk:before {
    content: "\e941";
}
.icoagendo-menu-marketing:before {
    content: "\e94b";
}
.icoagendo-menu-download:before {
    content: "\e960";
}
.icoagendo-menu-gare:before {
    content: "\e9af";
}
.icoagendo-menu-gestionali:before {
    content: "\ea71";
}
.icoagendo-menu-iniz-comm:before {
    content: "\f024";
}

/* Incentivazioni */
.icoagendo-tutela-auto:before {
    content: "\e901";
}
.icoagendo-tutela-beni:before {
    content: "\e902";
}
.icoagendo-tutela-persona:before {
    content: "\e903";
}

/* UI stuff */
.icoagendo-user-ghost:before {
    content: "\e905";
}
